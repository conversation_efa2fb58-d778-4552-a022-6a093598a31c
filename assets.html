<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>素材库 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    .assets-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
    }
    .asset-card:hover .asset-actions {
      opacity: 1;
    }
    .asset-actions {
      opacity: 0;
      transition: opacity 0.2s;
    }
    
    /* 添加一些辅助样式 */
    .text-success {
      color: hsl(var(--su));
    }
    
    .text-error {
      color: hsl(var(--er));
    }
    
    .text-warning {
      color: hsl(var(--wa));
    }
    
    .text-neutral {
      color: hsl(var(--n));
    }

    /* 步骤图标样式 */
    .step:before {
      display: none !important; /* 隐藏原有的数字 */
    }
    
    .step .step-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 1.5rem;
      height: 1.5rem;
      background-color: #333;
      color: white;
      border-radius: 50%;
      position: absolute;
      left: -0.75rem;
      z-index: 10;
      font-size: 0.75rem;
    }
    
    /* 移动设备优化 */
    @media (max-width: 640px) {
      .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
      }
      .btn-analysis {
        min-width: 72px;
        white-space: nowrap;
      }
    }
    
    /* 添加Markdown样式 */
    #analysis-content {
      padding: 1rem;
    }
    
    #analysis-content h1 {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }
    
    #analysis-content h2 {
      font-size: 1.25rem;
      font-weight: bold;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }
    
    #analysis-content ul {
      list-style-type: disc;
      padding-left: 1.5rem;
      margin-bottom: 0.5rem;
    }
    
    #analysis-content ol {
      list-style-type: decimal;
      padding-left: 1.5rem;
      margin-bottom: 0.5rem;
    }
    
    #analysis-content li {
      margin-bottom: 0.25rem;
    }
    
    /* 移除status组件的阴影 */
    .status {
      box-shadow: none !important;
    }
    
    /* 骨架屏样式 */
    .hidden-content {
      display: none;
    }
    
    /* 闪烁效果 */
    @keyframes pulse {
      0% {
        opacity: 0.6;
      }
      50% {
        opacity: 1;
      }
      100% {
        opacity: 0.6;
      }
    }
    
    .skeleton {
      animation: pulse 1.5s infinite;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 0.375rem;
    }
    
    /* 可展开表格样式 */
    .expandable-row {
      cursor: pointer;
    }
    
    .expandable-row .expand-icon {
      transition: transform 0.2s;
    }
    
    .expandable-row.expanded .expand-icon {
      transform: rotate(90deg);
    }
    
    .subtable-container {
      overflow: hidden;
      max-height: 0;
      transition: max-height 0.3s ease-out;
      background-color: #fcfcfc;
    }
    
    .subtable-container.expanded {
      max-height: 500px;
      transition: max-height 0.5s ease-in;
    }
    
    .subtable {
      margin: 0;
      background-color: #fcfcfc;
      width: 100%;
      min-width: 100%;
      table-layout: auto;
    }
    
    /* 子表格内列宽设置 */
    .subtable th:nth-child(2), 
    .subtable td:nth-child(2) {
      min-width: 180px; /* 子表格标题列 */
    }
    
    .subtable th:nth-child(3), 
    .subtable td:nth-child(3) {
      min-width: 120px; /* 子表格脚本类型列 */
    }
    
    .subtable th:nth-child(4), 
    .subtable td:nth-child(4) {
      min-width: 140px; /* 子表格爆款元素列 */
    }
    
    .subtable th:nth-child(5), 
    .subtable td:nth-child(5) {
      min-width: 100px; /* 子表格日期列 */
    }
    
    .subtable th:nth-child(6), 
    .subtable td:nth-child(6) {
      min-width: 120px; /* 子表格操作列 */
    }
    
    .subtable th, .subtable td {
      font-size: 0.9rem;
      padding: 0.75rem 1rem;
    }
    
    .subtable-inner {
      padding: 1rem;
      width: 100%;
    }
    
    .topic-badge {
      font-size: 0.7rem;
      padding: 0.15rem 0.5rem;
      border-radius: 1rem;
    }
    
    /* 添加/修改样式以固定操作栏 */
    .table-responsive {
      display: block;
      width: 100%;
      overflow-x: auto;
      position: relative;
      -webkit-overflow-scrolling: touch; /* 提高移动端滚动体验 */
      max-width: 100vw; /* 防止超出视口宽度 */
    }
    
    /* 确保表格内容不会超出表格容器 */
    .table-responsive table {
      width: 100%;
      margin-bottom: 0;
      table-layout: auto; /* 改用自动布局以适应内容 */
    }
    
    /* 移除不必要的表格容器类，统一使用table-responsive */
    .table-container {
      display: block;
      width: 100%;
      overflow-x: auto;
      position: relative;
      -webkit-overflow-scrolling: touch;
      max-width: 100vw;
      border-radius: 0.5rem;
      border: 1px solid #e5e7eb;
    }
    
    /* 标签样式优化 */
    .tag-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.25rem;
    }
    
    .tag-item {
      font-size: 0.7rem;
      padding: 0.15rem 0.4rem;
      border-radius: 1rem;
      background-color: #f3f4f6;
      color: #4b5563;
      white-space: nowrap;
    }
    
    /* 子表格样式 */
    .subtable-container {
      overflow: hidden;
      max-height: 0;
      transition: max-height 0.3s ease-out;
      background-color: #fcfcfc;
    }
    
    .subtable-container.expanded {
      max-height: 500px;
      transition: max-height 0.5s ease-in;
    }
    
    .subtable {
      margin: 0;
      background-color: #fcfcfc;
      width: 100%;
      min-width: 100%;
      table-layout: auto;
    }
    
    /* 子表格内列宽设置 */
    .subtable th:nth-child(2), 
    .subtable td:nth-child(2) {
      min-width: 180px; /* 子表格标题列 */
    }
    
    .subtable th:nth-child(3), 
    .subtable td:nth-child(3) {
      min-width: 120px; /* 子表格脚本类型列 */
    }
    
    .subtable th:nth-child(4), 
    .subtable td:nth-child(4) {
      min-width: 140px; /* 子表格爆款元素列 */
    }
    
    .subtable th:nth-child(5), 
    .subtable td:nth-child(5) {
      min-width: 100px; /* 子表格日期列 */
    }
    
    .subtable th:nth-child(6), 
    .subtable td:nth-child(6) {
      min-width: 120px; /* 子表格操作列 */
    }
    
    .subtable th, .subtable td {
      font-size: 0.9rem;
      padding: 0.75rem 1rem;
    }
    
    .subtable-inner {
      padding: 1rem;
      width: 100%;
    }
    
    /* Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
    
    /* 移动端标签容器最多显示2行 */
    @media (max-width: 768px) {
      .tag-container {
        max-height: 2.5rem;
        overflow: hidden;
      }
    }
    
    /* 表格列宽全局设置 - 使用Tailwind伪类替代CSS */
    /* 
    .col-cover {
      width: 100px;
      min-width: 100px;
    }
    
    .col-title {
      min-width: 180px;
    }
    
    .col-source {
      min-width: 80px;
    }
    
    .col-tags {
      min-width: 120px;
    }
    
    .col-author {
      min-width: 120px;
    }
    
    .col-link {
      min-width: 100px;
    }
    
    .col-status {
      min-width: 130px;
    }
    
    .col-analysis {
      min-width: 100px;
    }
    
    .col-script-type {
      min-width: 140px;
    }
    
    .col-elements {
      min-width: 160px;
    }
    
    .col-topic-status {
      min-width: 140px;
    }
    
    .col-date {
      min-width: 110px;
    }
    
    .col-action {
      min-width: 120px;
    }
    */
    
    /* 表格操作按钮样式 */
    .table-action-btn {
      min-width: 90px;
      white-space: nowrap;
    }
    
    /* 确保子表格宽度与主表格一致 */
    .subtable-container.table-responsive {
      border: none;
      border-radius: 0;
      width: 100%;
      max-width: 100%;
    }
    
    /* 确保子表格中的表格扩展到完整宽度 */
    .subtable-inner {
      width: 100%;
      max-width: 100%;
    }
    
    /* 移除可能导致宽度不一致的内边距 */
    .subtable-row td {
      padding: 0 !important;
    }
    
    /* 确保子表格行有适当的高度 */
    .subtable tr {
      height: 3rem;
    }
    
    /* 增加子表格标题与内容之间的距离 */
    .subtable-inner .flex.items-center.mb-2 {
      margin-bottom: 0.75rem;
    }
    
    /* 增大关联选题标题字号 */
    .subtable-inner h3.text-sm {
      font-size: 1rem;
      margin-bottom: 0.5rem;
    }
    

    
    /* 视频封面样式 */
    .video-cover {
      width: 40px;
      height: 71px; /* 保持9:16比例 */
      object-fit: cover;
      border-radius: 4px;
      background-color: #f3f4f6;
      position: relative;
      flex-shrink: 0;
      overflow: hidden;
    }
    
    .video-cover img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
    
    /* 移除旧的hover效果，使用新的hover效果 */
    
    /* 视频信息布局样式 */
    .video-info-container {
      display: flex;
      gap: 0.75rem;
      align-items: center;
      width: 100%;
    }
    
    .video-text-container {
      display: flex;
      flex-direction: column;
      width: calc(100% - 50px);
    }
    
    .video-title {
      font-weight: 500;
      line-height: 1.2;
      margin-bottom: 0.25rem;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      white-space: normal;
      max-height: 2.4em;
    }
    
    /* 标题悬浮时使用下划线 */
    a:hover .video-title {
      text-decoration: underline;
      color: inherit;
    }
    
    /* 互动数据样式 */
    .video-meta {
      display: flex;
      align-items: center;
      font-size: 0.75rem;
      color: rgba(0, 0, 0, 0.6);
      flex-wrap: wrap;
    }
    
    .video-stats {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      margin-left: 4px;
    }
    
    .video-stats i,
    .video-stats span {
      color: #999;
      font-size: 0.75rem;
    }
    
    .video-stats > div {
      display: flex;
      align-items: center;
      gap: 2px;
    }
    
    .video-source {
      font-size: 0.75rem;
      color: rgba(0, 0, 0, 0.6);
    }
    
    .video-publish-time {
      font-size: 0.75rem;
      color: rgba(0, 0, 0, 0.6);
    }
    
    /* 作者头像样式 */
    .author-container {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .author-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      object-fit: cover;
    }
    
    .author-name {
      font-size: 0.875rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    /* 骨架屏视频列样式 */
    .skeleton-video {
      display: flex;
      gap: 0.75rem;
      align-items: center;
      width: 100%;
    }
    
    .skeleton-cover {
      width: 40px;
      height: 71px;
      border-radius: 4px;
      flex-shrink: 0;
    }
    
    .skeleton-text {
      width: calc(100% - 50px);
    }
    
    .skeleton-title {
      height: 1rem;
      width: 90%;
    }
    
    .skeleton-meta {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: 0.25rem;
    }
    
    .skeleton-source {
      height: 0.75rem;
      width: 40%;
    }
    
    .skeleton-time {
      height: 0.75rem;
      width: 35%;
    }
    
    /* 确保封面链接覆盖整个封面区域且点击正常 */
    .video-cover {
      position: relative;
      overflow: hidden;
    }
    
    .video-cover a,
    #material-cover-container a {
      display: block;
      width: 100%;
      height: 100%;
      position: relative;
      z-index: 1;
    }
    
    /* 确保封面容器有正确的定位 */
    #material-cover-container {
      position: relative;
      overflow: hidden;
    }
    
    /* 添加封面hover效果 */
    .video-cover a::after,
    #material-cover-container a::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0;
      transition: opacity 0.2s ease;
      border-radius: 4px;
      z-index: 2;
    }
    
    .video-cover a:hover::after,
    #material-cover-container a:hover::after {
      opacity: 1;
    }
    
    .video-cover a::before,
    #material-cover-container a::before {
      content: '\f04b';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      z-index: 3;
      opacity: 0;
      transition: opacity 0.2s ease;
      font-size: 1.2rem;
      pointer-events: none;
    }
    
    .video-cover a:hover::before,
    #material-cover-container a:hover::before {
      opacity: 1;
    }
    
    /* 详情页封面特殊处理 */
    #material-cover-container a::before {
      font-size: 1.5rem; /* 详情页封面更大，图标也相应调整 */
    }
    
    /* 详情页标题hover效果 */
    #material-title {
      transition: text-decoration 0.2s ease;
    }
    
    #material-link-title:hover #material-title {
      text-decoration: underline;
    }
    
    /* 作者头像圆形样式 */
    #material-author-avatar {
      border-radius: 50%;
      object-fit: cover;
    }
    
    /* 确保所有链接能够点击并阻止事件冒泡 */
    .video-info-container a,
    .author-container a {
      position: relative;
      z-index: 2;
    }
    
    /* 保持行可点击的视觉提示 */
    tr[data-material-id] {
      cursor: pointer;
    }
    
    /* Prose样式优化 */
    .prose {
      color: inherit;
      max-width: none !important;
    }
    
    .prose h1, .prose h2, .prose h3, .prose h4 {
      color: inherit;
      margin-bottom: 0.75rem;
      margin-top: 1.5rem;
    }
    
    .prose h1:first-child, .prose h2:first-child, .prose h3:first-child, .prose h4:first-child {
      margin-top: 0;
    }
    
    .prose p {
      margin-bottom: 1rem;
      line-height: 1.6;
    }
    
    .prose ul, .prose ol {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
    }
    
    .prose li {
      margin-bottom: 0.25rem;
    }
    
    .prose strong {
      font-weight: 600;
      color: inherit;
    }
    
    .prose em {
      font-style: italic;
    }
    
    .prose code {
      background-color: rgba(0, 0, 0, 0.1);
      padding: 0.125rem 0.25rem;
      border-radius: 0.25rem;
      font-size: 0.875em;
    }
    
    .prose blockquote {
      border-left: 4px solid #e5e7eb;
      padding-left: 1rem;
      margin: 1rem 0;
      font-style: italic;
      color: inherit;
      opacity: 0.8;
    }
    
    .prose table {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
    }
    
    .prose th, .prose td {
      border: 1px solid #e5e7eb;
      padding: 0.5rem;
      text-align: left;
    }
    
    .prose th {
      background-color: rgba(0, 0, 0, 0.05);
      font-weight: 600;
    }
    
    /* 移动端优化 */
    @media (max-width: 640px) {
      .prose {
        font-size: 0.875rem;
      }
      
      .prose h1 {
        font-size: 1.25rem;
      }
      
      .prose h2 {
        font-size: 1.125rem;
      }
      
      .prose h3 {
        font-size: 1rem;
      }
      
      .prose h4 {
        font-size: 0.875rem;
      }
      
      .prose ul, .prose ol {
        padding-left: 1rem;
      }
      
      .prose table {
        font-size: 0.75rem;
      }
      
      .prose th, .prose td {
        padding: 0.25rem;
      }
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">素材库</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <header class="mb-6">
          <h1 class="text-2xl font-bold hidden lg:block">素材库</h1>
          <p class="text-base-content opacity-60 hidden lg:block">素材积累的厚度，决定创作的高度</p>
        </header>
        
        <!-- 操作和筛选区 -->
        <div class="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div class="flex flex-wrap gap-2 items-center">
            <button class="btn bg-black text-white hover:bg-gray-800" onclick="add_material_modal.showModal()">
              <i class="fas fa-upload mr-2"></i>手动添加素材
            </button>
            </div>
          </div>
          
        <!-- 素材展示区 -->
        <div class="mb-6">
          <!-- 视图切换 -->
                      <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold">所有素材</h2>
            <div class="join">
              <input type="text" placeholder="搜索素材标题或脚本类型" class="input input-bordered input-sm join-item w-64" />
              <button class="btn btn-sm join-item">
                <i class="fas fa-search"></i>
              </button>
          </div>
        </div>
          
          <!-- 素材骨架屏 -->
          <div id="skeleton-materials">
            <div class="overflow-x-auto rounded-box border border-base-300 bg-base-100 table-responsive">
              <table class="table w-full">
                <thead>
                  <tr class="border-b-2 border-base-300">
                    <th class="col-video min-w-[320px]"><div class="skeleton h-4 w-40"></div></th>
                    <th class="col-author min-w-[120px]"><div class="skeleton h-4 w-20"></div></th>
                    <th class="col-tags min-w-[150px]"><div class="skeleton h-4 w-24"></div></th>
                    <th class="col-script-type min-w-[150px]"><div class="skeleton h-4 w-20"></div></th>
                    <th class="col-elements min-w-[150px]"><div class="skeleton h-4 w-20"></div></th>
                    <th class="col-date min-w-[110px]"><div class="skeleton h-4 w-16"></div></th>
                    <th class="col-action min-w-[120px]"><div class="skeleton h-4 w-12"></div></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <div class="skeleton-video">
                        <div class="skeleton skeleton-cover"></div>
                        <div class="skeleton-text">
                          <div class="skeleton skeleton-title"></div>
                          <div class="skeleton-meta">
                            <div class="skeleton skeleton-source"></div>
                            <div class="skeleton skeleton-time"></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td><div class="skeleton h-8 w-24"></div></td>
                    <td><div class="skeleton h-4 w-20"></div></td>
                    <td><div class="skeleton h-4 w-24"></div></td>
                    <td><div class="skeleton h-4 w-20"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td><div class="skeleton h-6 w-16"></div></td>
                  </tr>
                  <tr>
                    <td>
                      <div class="skeleton-video">
                        <div class="skeleton skeleton-cover"></div>
                        <div class="skeleton-text">
                          <div class="skeleton skeleton-title"></div>
                          <div class="skeleton-meta">
                            <div class="skeleton skeleton-source"></div>
                            <div class="skeleton skeleton-time"></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td><div class="skeleton h-8 w-24"></div></td>
                    <td><div class="skeleton h-4 w-20"></div></td>
                    <td><div class="skeleton h-4 w-24"></div></td>
                    <td><div class="skeleton h-4 w-20"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td><div class="skeleton h-6 w-16"></div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- 实际素材内容 -->
          <div id="materials-content" class="hidden-content">
            <div class="table-responsive rounded-box border border-base-300">
              <table class="table bg-base-100 w-full">
                <!-- head -->
                <thead>
                  <tr class="border-b-2 border-base-300">
                    <th class="col-video min-w-[320px]">视频</th>
                    <th class="col-author min-w-[120px]">作者</th>
                    <th class="col-tags min-w-[150px]">视频标签</th>
                    <th class="col-script-type min-w-[150px]">脚本类型</th>
                    <th class="col-elements min-w-[150px]">爆款元素</th>
                    <th class="col-date min-w-[110px]">添加时间</th>
                    <th class="col-action min-w-[120px]">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- 素材1 -->
                  <tr class="hover:bg-base-200 transition-colors duration-200 cursor-pointer" data-material-id="M001" onclick="openDetailDrawer('M001')">
                    <td>
                      <div class="video-info-container">
                        <div class="video-cover">
                          <a href="https://www.douyin.com/video/7123456789012345678" target="_blank" onclick="event.stopPropagation();">
                            <img src="https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187" 
                                alt="视频封面" />
                          </a>
                        </div>
                        <div class="video-text-container">
                          <a href="https://www.douyin.com/video/7123456789012345678" target="_blank" onclick="event.stopPropagation();">
                            <div class="video-title line-clamp-2 whitespace-normal" title="新房装修完，怎么除甲醛？#装修 #装修避坑 #除甲醛 #桃气炭长 #桃气出击醛菌覆灭">
                              新房装修完，怎么除甲醛？这5个除甲醛方法超实用，比通风更有效 #装修 #装修避坑 #除甲醛
                            </div>
                          </a>
                          <div class="video-meta">
                            <span class="video-source">手动</span>
                            <span class="mx-1">•</span>
                            <span class="video-publish-time">2023-11-25 14:30</span>
                            <span class="mx-1">•</span>
                            <div class="video-stats">
                              <div><i class="fas fa-heart text-xs"></i> <span>3.6万</span></div>
                              <div><i class="fas fa-comment text-xs"></i> <span>5636</span></div>
                              <div><i class="fas fa-star text-xs"></i> <span>2235</span></div>
                              <div><i class="fas fa-share text-xs"></i> <span>8596</span></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="author-container">
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();">
                          <img src="./assets/avatar_chon.jpg" alt="作者头像" class="author-avatar" onerror="this.src='./assets/default-avatar.jpg'">
                        </a>
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();" class="hover:underline">
                          <span class="author-name">装修达人</span>
                        </a>
                      </div>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">装修</span>
                        <span class="tag-item">室内</span>
                        <span class="tag-item">生活</span>
                      </div>
                    </td>
                    <td>解体型教知识脚本</td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">专业</span>
                        <span class="tag-item">实用</span>
                        <span class="tag-item">家居</span>
                      </div>
                    </td>
                    <td>2023-11-25</td>
                    <td>
                      <button class="btn btn-sm btn-outline table-action-btn" onclick="openDetailDrawer('M001'); event.stopPropagation();">查看详情</button>
                    </td>
                  </tr>
                  <!-- 使用右侧抽屉代替子表格，不需要子表格代码 -->
                  
                  <!-- 素材2 - 分析状态为进行中 -->
                  <tr class="hover:bg-base-200 transition-colors duration-200 cursor-pointer" data-material-id="M002" onclick="openDetailDrawer('M002')">
                    <td>
                      <div class="video-info-container">
                        <div class="video-cover">
                          <a href="https://www.douyin.com/video/7234567890123456789" target="_blank" onclick="event.stopPropagation();">
                            <img src="https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187" 
                              alt="视频封面" />
                          </a>
                        </div>
                        <div class="video-text-container">
                          <a href="https://www.douyin.com/video/7234567890123456789" target="_blank" onclick="event.stopPropagation();">
                            <div class="video-title line-clamp-2 whitespace-normal" title="《欣赏优秀视频之强记女工，全女维修团队真的太棒了》：视频不想做可以不做，没必要硬夸# 孙笑川 # 抽象 # 逆天">
                              《欣赏优秀视频之强记女工，全女维修团队真的太棒了》：视频不想做可以不做，没必要硬夸
                            </div>
                          </a>
                          <div class="video-meta">
                            <span class="video-source">收藏夹</span>
                            <span class="mx-1">•</span>
                            <span class="video-publish-time">2023-11-24 09:15</span>
                            <span class="mx-1">•</span>
                            <div class="video-stats">
                              <div><i class="fas fa-heart text-xs"></i> <span>1.2万</span></div>
                              <div><i class="fas fa-comment text-xs"></i> <span>1532</span></div>
                              <div><i class="fas fa-star text-xs"></i> <span>857</span></div>
                              <div><i class="fas fa-share text-xs"></i> <span>2365</span></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="author-container">
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();">
                          <img src="./assets/avatar_jinqiangdashu.jpeg" alt="作者头像" class="author-avatar" onerror="this.src='./assets/default-avatar.jpg'">
                        </a>
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();" class="hover:underline">
                          <span class="author-name">技术百科</span>
                        </a>
                      </div>
                    </td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>2023-11-18</td>
                    <td>
                      <div class="flex items-center gap-1">
                        <div class="inline-grid *:[grid-area:1/1]">
                          <div class="status status-warning animate-ping"></div>
                          <div class="status status-warning"></div>
                        </div>
                        <span>素材分析中</span>
                      </div>
                    </td>
                  </tr>

                  
                  <!-- 素材3 -->
                  <tr class="hover:bg-base-200 transition-colors duration-200 cursor-pointer" data-material-id="M003" onclick="openDetailDrawer('M003')">
                    <td>
                      <div class="video-info-container">
                        <div class="video-cover">
                          <a href="https://www.douyin.com/video/7345678901234567890" target="_blank" onclick="event.stopPropagation();">
                            <img src="https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187" 
                              alt="视频封面" />
                          </a>
                        </div>
                        <div class="video-text-container">
                          <a href="https://www.douyin.com/video/7345678901234567890" target="_blank" onclick="event.stopPropagation();">
                            <div class="video-title line-clamp-2 whitespace-normal" title="如何提高工作效率？5个实用小技巧 #效率 #工作 #时间管理">
                              如何提高工作效率？5个实用小技巧 #效率 #工作 #时间管理
                            </div>
                          </a>
                          <div class="video-meta">
                            <span class="video-source">手动</span>
                            <span class="mx-1">•</span>
                            <span class="video-publish-time">2023-11-23 16:45</span>
                            <span class="mx-1">•</span>
                            <div class="video-stats">
                              <div><i class="fas fa-heart text-xs"></i> <span>2.8万</span></div>
                              <div><i class="fas fa-comment text-xs"></i> <span>3102</span></div>
                              <div><i class="fas fa-star text-xs"></i> <span>4566</span></div>
                              <div><i class="fas fa-share text-xs"></i> <span>1987</span></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="author-container">
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();">
                          <img src="./assets/avatar_fangqi_kiki.jpeg" alt="作者头像" class="author-avatar" onerror="this.src='./assets/default-avatar.jpg'">
                        </a>
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();" class="hover:underline">
                          <span class="author-name">效率专家</span>
                        </a>
                      </div>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">工作效率</span>
                        <span class="tag-item">时间管理</span>
                        <span class="tag-item">职场技巧</span>
                      </div>
                    </td>
                    <td>知识分享脚本</td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">效率</span>
                        <span class="tag-item">职场</span>
                        <span class="tag-item">技巧</span>
                      </div>
                    </td>
                    <td>2023-11-23</td>
                    <td>
                      <button class="btn btn-sm btn-outline table-action-btn" onclick="openDetailDrawer('M003'); event.stopPropagation();">查看详情</button>
                    </td>
                  </tr>

                  
                  <!-- 素材4 - 分析状态为待处理 -->
                  <tr class="hover:bg-base-200 transition-colors duration-200 cursor-pointer" data-material-id="M004" onclick="openDetailDrawer('M004')">
                    <td>
                      <div class="video-info-container">
                        <div class="video-cover">
                          <a href="https://www.douyin.com/video/7456789012345678901" target="_blank" onclick="event.stopPropagation();">
                            <img src="https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187" 
                              alt="视频封面" />
                          </a>
                        </div>
                        <div class="video-text-container">
                          <a href="https://www.douyin.com/video/7456789012345678901" target="_blank" onclick="event.stopPropagation();">
                            <div class="video-title line-clamp-2 whitespace-normal" title="这些摄影小技巧让你的照片更专业 #摄影技巧 #手机摄影 #构图">
                              这些摄影小技巧让你的照片更专业 #摄影技巧 #手机摄影 #构图
                            </div>
                          </a>
                          <div class="video-meta">
                            <span class="video-source">收藏夹</span>
                            <span class="mx-1">•</span>
                            <span class="video-publish-time">2023-11-22 11:20</span>
                            <span class="mx-1">•</span>
                            <div class="video-stats">
                              <div><i class="fas fa-heart text-xs"></i> <span>8752</span></div>
                              <div><i class="fas fa-comment text-xs"></i> <span>923</span></div>
                              <div><i class="fas fa-star text-xs"></i> <span>1428</span></div>
                              <div><i class="fas fa-share text-xs"></i> <span>652</span></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="author-container">
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();">
                          <img src="./assets/avatar_fangqi_kiki.jpeg" alt="作者头像" class="author-avatar" onerror="this.src='./assets/default-avatar.jpg'">
                        </a>
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();" class="hover:underline">
                          <span class="author-name">摄影小技巧</span>
                        </a>
                      </div>
                    </td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>2023-11-22</td>
                    <td>
                      <div class="flex items-center gap-1">
                        <div class="inline-grid *:[grid-area:1/1]">
                          <div class="status animate-ping"></div>
                          <div class="status"></div>
                        </div>
                        <span>待处理</span>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- 素材5 - 素材拆片中 -->
                  <tr class="hover:bg-base-200 transition-colors duration-200 cursor-pointer" data-material-id="M005" onclick="openDetailDrawer('M005')">
                    <td>
                      <div class="video-info-container">
                        <div class="video-cover">
                          <a href="https://www.douyin.com/video/7567890123456789012" target="_blank" onclick="event.stopPropagation();">
                            <img src="https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187" 
                              alt="视频封面" />
                          </a>
                        </div>
                        <div class="video-text-container">
                          <a href="https://www.douyin.com/video/7567890123456789012" target="_blank" onclick="event.stopPropagation();">
                            <div class="video-title line-clamp-2 whitespace-normal" title="三款超适合夏天的清爽饮品，简单易做 #饮品 #夏季 #健康">
                              三款超适合夏天的清爽饮品，简单易做 #饮品 #夏季 #健康
                            </div>
                          </a>
                          <div class="video-meta">
                            <span class="video-source">收藏夹</span>
                            <span class="mx-1">•</span>
                            <span class="video-publish-time">2023-11-21 09:45</span>
                            <span class="mx-1">•</span>
                            <div class="video-stats">
                              <div><i class="fas fa-heart text-xs"></i> <span>1.5万</span></div>
                              <div><i class="fas fa-comment text-xs"></i> <span>2348</span></div>
                              <div><i class="fas fa-star text-xs"></i> <span>3612</span></div>
                              <div><i class="fas fa-share text-xs"></i> <span>845</span></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="author-container">
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();">
                          <img src="./assets/avatar_chon.jpg" alt="作者头像" class="author-avatar" onerror="this.src='./assets/default-avatar.jpg'">
                        </a>
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();" class="hover:underline">
                          <span class="author-name">美食日记</span>
                        </a>
                      </div>
                    </td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>2023-11-21</td>
                    <td>
                      <div class="flex items-center gap-1">
                        <div class="inline-grid *:[grid-area:1/1]">
                          <div class="status status-info animate-ping"></div>
                          <div class="status status-info"></div>
                      </div>
                        <span>素材拆片中</span>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- 素材6 - 选题生成中 -->
                  <tr class="hover:bg-base-200 transition-colors duration-200 cursor-pointer" data-material-id="M006" onclick="openDetailDrawer('M006')">
                    <td>
                      <div class="video-info-container">
                        <div class="video-cover">
                          <a href="https://www.douyin.com/video/7678901234567890123" target="_blank" onclick="event.stopPropagation();">
                            <img src="https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187" 
                              alt="视频封面" />
                          </a>
                        </div>
                        <div class="video-text-container">
                          <a href="https://www.douyin.com/video/7678901234567890123" target="_blank" onclick="event.stopPropagation();">
                            <div class="video-title line-clamp-2 whitespace-normal" title="家庭整理收纳的10个小窍门 #收纳 #整理 #家居">
                              家庭整理收纳的10个小窍门 #收纳 #整理 #家居
                            </div>
                          </a>
                          <div class="video-meta">
                            <span class="video-source">手动</span>
                            <span class="mx-1">•</span>
                            <span class="video-publish-time">2023-11-20 16:30</span>
                            <span class="mx-1">•</span>
                            <div class="video-stats">
                              <div><i class="fas fa-heart text-xs"></i> <span>5.2万</span></div>
                              <div><i class="fas fa-comment text-xs"></i> <span>8463</span></div>
                              <div><i class="fas fa-star text-xs"></i> <span>1.2万</span></div>
                              <div><i class="fas fa-share text-xs"></i> <span>7826</span></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="author-container">
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();">
                          <img src="./assets/avatar_fangqi_kiki.jpeg" alt="作者头像" class="author-avatar" onerror="this.src='./assets/default-avatar.jpg'">
                        </a>
                        <a href="https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA" target="_blank" onclick="event.stopPropagation();" class="hover:underline">
                          <span class="author-name">生活妙招</span>
                        </a>
                      </div>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">收纳</span>
                        <span class="tag-item">整理</span>
                        <span class="tag-item">家居</span>
                      </div>
                    </td>
                    <td>干货型脚本</td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">实用</span>
                        <span class="tag-item">居家</span>
                        <span class="tag-item">收纳</span>
                      </div>
                    </td>
                    <td>2023-11-20</td>
                    <td>
                      <div class="flex items-center gap-1">
                        <div class="inline-grid *:[grid-area:1/1]">
                          <div class="status status-success animate-ping"></div>
                          <div class="status status-success"></div>
                        </div>
                        <span>选题生成中</span>
                      </div>
                    </td>
                  </tr>

                  


                </tbody>
              </table>
            </div>
          </div>
          
         </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side z-50">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">28</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item menu-active w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- URL 模态框 -->
  <dialog id="url-modal" class="modal">
    <div class="modal-box">
      <h3 class="font-bold text-lg">视频链接</h3>
      <div class="py-4">
        <p id="url-content" class="text-sm break-all"></p>
      </div>
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
      </div>
    </div>
  </dialog>
  
  <!-- 分析结果模态框 -->
  <dialog id="analysis-modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box w-full max-w-4xl max-h-[90vh]">
      <div class="flex justify-between items-center mb-4 sticky top-0 bg-base-100 z-10 pb-2 border-b border-base-300">
        <h3 class="text-lg font-bold">素材分析结果</h3>
        <form method="dialog">
          <button class="btn btn-sm btn-ghost">
            <i class="fas fa-times"></i>
          </button>
        </form>
      </div>
      
      <div class="overflow-y-auto max-h-[calc(90vh-8rem)]">
        <div class="prose prose-sm max-w-none" id="analysis-content">
          <!-- Markdown内容将通过JavaScript动态加载 -->
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">视频内容概述</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
          
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">脚本结构分析</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
          
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">爆款元素识别</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
          
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">变现潜力评估</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
        </div>
      </div>
      
      <div class="modal-action sticky bottom-0 bg-base-100 pt-4 border-t border-base-300 mt-4">
        <form method="dialog">
          <button class="btn btn-block sm:btn-auto">关闭</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 小屏幕详情模态框 -->
  <dialog id="details-modal" class="modal">
    <div class="modal-box">
      <h3 class="font-bold text-lg">素材详情</h3>
      <div class="py-4">
        <div id="details-content" class="text-sm space-y-2">
          <!-- 动态生成内容 -->
        </div>
      </div>
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
      </div>
    </div>
  </dialog>
  
  <!-- 添加素材模态框 -->
  <dialog id="add_material_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">手动添加素材</h3>
      <div class="mb-4">
        <div class="text-base font-medium mb-2">抖音链接</div>
        <textarea class="textarea textarea-bordered w-full h-24" placeholder="抖音视频的分享链接"></textarea>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2" onsubmit="addMaterial(); return false;">
          <button type="button" class="btn">取消</button>
          <button type="submit" class="btn bg-black text-white hover:bg-gray-800" title="按 Enter 提交">确定</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 确认选题确认模态框 -->
  <dialog id="confirm_topic_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认选题</h3>
      <p>确认选题后将添加到选题库中，同时开始生成文案</p>
      <input type="hidden" id="confirm-topic-id" value="">
      <input type="hidden" id="confirm-topic-btn" value="">
      <div class="modal-action">
        <form method="dialog" class="flex gap-2" onsubmit="confirmTopicFinal(); return false;">
          <button type="button" class="btn">取消</button>
          <button type="submit" class="btn bg-black text-white hover:bg-gray-800" title="按 Enter 提交">确认</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 撤销选题确认模态框 -->
  <dialog id="cancel_topic_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">撤销选题</h3>
      <p>确认要撤销这个选题吗？撤销后将从选题库中移除</p>
      <input type="hidden" id="cancel-topic-id" value="">
      <div class="modal-action">
        <form method="dialog" class="flex gap-2" onsubmit="cancelTopicFinal(); return false;">
          <button type="button" class="btn">取消</button>
          <button type="submit" class="btn btn-error text-white" title="按 Enter 提交">确认撤销</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 素材分析结果模态框 -->
  <dialog id="text-modal" class="modal">
    <div class="modal-box">
      <h3 class="font-bold text-lg">素材分析结果</h3>
      <div class="py-4">
        <p id="text-content" class="text-sm break-all whitespace-pre-wrap"></p>
      </div>
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
      </div>
    </div>
  </dialog>
  
  <!-- 再来五条模态框 -->
  <dialog id="more-topics-modal" class="modal">
    <div class="modal-box max-w-lg">
      <h3 class="font-bold text-lg">再次生成选题</h3>
      <div class="space-y-4 mt-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">参考素材</span>
          </label>
          <input type="text" id="reference-material" class="input input-bordered w-full" disabled />
          <input type="hidden" id="current-material-id" />
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">脚本类型</span>
          </label>
          <input type="text" id="script-type" class="input input-bordered w-full" value="教知识脚本" placeholder="例如：干货型脚本" />
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">用户要求（选填）</span>
          </label>
          <textarea id="user-requirements" class="textarea textarea-bordered w-full h-32" placeholder="例如：标题中包含 xxx，加入 xx 元素等"></textarea>
        </div>
      </div>
      <div class="modal-action">
        <form class="flex gap-2" onsubmit="generateMoreTopics(); document.getElementById('more-topics-modal').close(); return false;">
          <button type="button" class="btn btn-outline" onclick="document.getElementById('more-topics-modal').close()">取消</button>
          <button type="submit" class="btn btn-neutral" title="按 Enter 提交">确认再次生成</button>
        </form>
      </div>
    </div>
  </dialog>
  
  <!-- 骨架屏加载脚本 -->
  <script>
    // 当前选中的素材ID
    let currentMaterialId = '';
    
    document.addEventListener('DOMContentLoaded', function() {
      // 模拟加载延迟
      setTimeout(function() {
        // 显示素材内容
        const skeletonMaterials = document.getElementById('skeleton-materials');
        const materialsContent = document.getElementById('materials-content');
        
        if (skeletonMaterials && materialsContent) {
          // 隐藏骨架屏
          skeletonMaterials.style.display = 'none';
          
          // 显示素材内容
          materialsContent.classList.remove('hidden-content');
          
          // 初始化确认选题按钮
          initConfirmTopicButtons();
          
          // 更新侧边栏数量
          updateSidebarCounts();
          
          // 确保所有链接能够点击并阻止事件冒泡
          const links = document.querySelectorAll('.video-cover a, .video-text-container a, .author-container a');
          links.forEach(link => {
            if (!link.hasAttribute('onclick')) {
              link.setAttribute('onclick', 'event.stopPropagation();');
            }
          });
        }
      }, 300); // 300毫秒后显示内容
      
      // 添加键盘事件监听器
      document.addEventListener('keydown', function(event) {
        // ESC键关闭drawer
        if (event.key === 'Escape') {
          // 首先检查是否有打开的modal，优先关闭modal
          const openModals = document.querySelectorAll('dialog[open]');
          if (openModals.length > 0) {
            return; // 让modal自己处理ESC
          }
          
          // 如果没有modal打开，则关闭drawer
          const drawerDetail = document.getElementById('drawer-detail');
          if (drawerDetail && drawerDetail.checked) {
            drawerDetail.checked = false;
            event.preventDefault();
            event.stopPropagation();
            
            // 添加一个小延迟确保状态更新
            setTimeout(() => {
              drawerDetail.blur(); // 移除焦点
            }, 10);
          }
        }
        
        // Enter键处理 - 只处理特定情况，避免冲突
        if (event.key === 'Enter') {
          const activeElement = document.activeElement;
          
          // 只处理搜索框的Enter键
          if (activeElement && activeElement.tagName === 'INPUT' && 
              activeElement.placeholder && activeElement.placeholder.includes('搜索')) {
            const searchBtn = activeElement.parentElement.querySelector('button');
            if (searchBtn) {
              searchBtn.click();
              event.preventDefault();
            }
            return;
          }
          
          // 对于modal中的表单，让浏览器默认行为处理
          // 不手动处理Enter键提交，避免冲突
        }
      });
    });
    
    // 打开素材详情抽屉
    function openDetailDrawer(materialId) {
      // 获取素材状态
      const materials = {
        'M001': { status: 'success' },
        'M002': { status: 'analyzing' },
        'M003': { status: 'success' },
        'M004': { status: 'pending' },
        'M005': { status: 'dissecting' },
        'M006': { status: 'generating' }
      };
      
      const material = materials[materialId] || { status: 'unknown' };
      
      // 只有处理成功的素材才能查看详情
      if (material.status === 'success') {
        // 保存当前素材ID
        currentMaterialId = materialId;
        
        // 打开抽屉
        document.getElementById('drawer-detail').checked = true;
        
        // 根据materialId加载对应数据
        loadMaterialDetail(materialId);
        
        // 切换按钮可见性
        toggleActionButtons(materialId);
      } else {
        // 对于未完成处理的素材，显示相应提示
        showProcessingToast(material.status);
      }
    }
    
    // 显示处理中提示
    function showProcessingToast(status) {
      const messages = {
        'analyzing': '素材分析中，请稍候...',
        'pending': '素材待处理，请稍后...',
        'dissecting': '素材拆片中，请稍候...',
        'generating': '正在生成选题，请稍候...',
        'unknown': '素材状态未知'
      };
      
      const message = messages[status] || '素材还在处理中';
      showToast(message, 'info');
    }
    
    // 加载素材详情
    function loadMaterialDetail(materialId) {
      // 素材数据（示例数据，实际应用中应从服务器获取）
      const materials = {
        'M001': {
          title: '新房装修完，怎么除甲醛？这5个除甲醛方法超实用，比通风更有效 #装修 #装修避坑 #除甲醛',
          cover: 'https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187',
          author: '装修达人',
          authorAvatar: './assets/avatar_chon.jpg',
          authorLink: 'https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA',
          publishTime: '2023-11-25 14:30',
          link: 'https://www.douyin.com/video/7123456789012345678',
          tags: ['装修', '室内', '生活'],
          scriptType: '解体型教知识脚本',
          elements: ['专业', '实用', '家居'],
          likes: '3.6万',
          comments: '5636',
          favorites: '2235',
          shares: '8596',
          topics: [
            { id: 'T001', title: '新房装修除甲醛最有效的5种方法，第3种最省钱!', scriptType: '干货型脚本', date: '2023-11-25', status: 'normal' },
            { id: 'T002', title: '新房入住前必做的除甲醛步骤，专家建议必看', scriptType: '解决方案类脚本', date: '2023-11-25', status: 'normal' },
            { id: 'T003', title: '除甲醛最有效的植物有哪些？这5种植物效果最好', scriptType: '榜单类脚本', date: '2023-11-24', status: 'normal' },
            { id: 'T004', title: '装修后甲醛超标怎么办？专业除醛指南', scriptType: '问题解决型脚本', date: '2023-11-24', status: 'confirmed' },
            { id: 'T005', title: '别再被骗了！这些除甲醛方法根本不管用', scriptType: '揭秘型脚本', date: '2023-11-23', status: 'normal' }
          ],
          status: 'success'
        },
        'M002': {
          title: '《欣赏优秀视频之强记女工，全女维修团队真的太棒了》：视频不想做可以不做，没必要硬夸# 孙笑川 # 抽象 # 逆天',
          cover: 'https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187',
          author: '技术百科',
          authorAvatar: './assets/avatar_jinqiangdashu.jpeg',
          authorLink: 'https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA',
          publishTime: '2023-11-24 09:15',
          link: 'https://www.douyin.com/video/7234567890123456789',
          tags: [],
          scriptType: '',
          elements: [],
          likes: '1.2万',
          comments: '1532',
          favorites: '857',
          shares: '2365',
          topics: [],
          status: 'analyzing'
        },
        'M003': {
          title: '如何提高工作效率？5个实用小技巧 #效率 #工作 #时间管理',
          cover: 'https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187',
          author: '效率专家',
          authorAvatar: './assets/avatar_fangqi_kiki.jpeg',
          authorLink: 'https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA',
          publishTime: '2023-11-23 16:45',
          link: 'https://www.douyin.com/video/7345678901234567890',
          tags: ['工作效率', '时间管理', '职场技巧'],
          scriptType: '知识分享脚本',
          elements: ['效率', '职场', '技巧'],
          likes: '2.8万',
          comments: '3102',
          favorites: '4566',
          shares: '1987',
          topics: [
            { id: 'T006', title: '办公室工作效率翻倍的5个技巧，同事都用起来了', scriptType: '干货型脚本', date: '2023-11-23', status: 'normal' },
            { id: 'T007', title: '效率高手都在用的时间管理法，让你工作更轻松', scriptType: '教程型脚本', date: '2023-11-22', status: 'normal' },
            { id: 'T008', title: '专注力训练：让你的工作效率提升200%', scriptType: '实用技巧型脚本', date: '2023-11-22', status: 'confirmed' },
            { id: 'T009', title: '告别拖延症：高效人士都在用的3个秘诀', scriptType: '心理解析型脚本', date: '2023-11-21', status: 'normal' },
            { id: 'T010', title: '用这款APP提升工作效率，我每天省下2小时', scriptType: '工具分享型脚本', date: '2023-11-21', status: 'normal' }
          ],
          status: 'success'
        },
        'M004': {
          title: '这些摄影小技巧让你的照片更专业 #摄影技巧 #手机摄影 #构图',
          cover: 'https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187',
          author: '摄影小技巧',
          authorAvatar: './assets/avatar_fangqi_kiki.jpeg',
          authorLink: 'https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA',
          publishTime: '2023-11-22 11:20',
          link: 'https://www.douyin.com/video/7456789012345678901',
          tags: [],
          scriptType: '',
          elements: [],
          likes: '8752',
          comments: '923',
          favorites: '1428',
          shares: '652',
          topics: [],
          status: 'pending'
        },
        'M005': {
          title: '三款超适合夏天的清爽饮品，简单易做 #饮品 #夏季 #健康',
          cover: 'https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187',
          author: '美食日记',
          authorAvatar: './assets/avatar_chon.jpg',
          authorLink: 'https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA',
          publishTime: '2023-11-21 09:45',
          link: 'https://www.douyin.com/video/7567890123456789012',
          tags: [],
          scriptType: '',
          elements: [],
          likes: '1.5万',
          comments: '2348',
          favorites: '3612',
          shares: '845',
          topics: [],
          status: 'dissecting'
        },
        'M006': {
          title: '家庭整理收纳的10个小窍门 #收纳 #整理 #家居',
          cover: 'https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/719c7dfaa5644481a96813308c34208a~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=l4LZ8mVyeD4fQAogQy3naDN1ye8%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=202505192144153EED8D045ED26D11D187',
          author: '生活妙招',
          authorAvatar: './assets/avatar_fangqi_kiki.jpeg',
          authorLink: 'https://www.douyin.com/user/MS4wLjABAAAA6S1IqPN_vfnt9xpQEer4lip2Z787TlIG9N9fsOrAQvA',
          publishTime: '2023-11-20 16:30',
          link: 'https://www.douyin.com/video/7678901234567890123',
          tags: [],
          scriptType: '',
          elements: [],
          likes: '5.2万',
          comments: '8463',
          favorites: '1.2万',
          shares: '7826',
          topics: [],
          status: 'generating'
        }
      };
      
      // 获取当前素材数据
      const material = materials[materialId] || {
        title: '未知素材',
        cover: '',
        author: '未知作者',
        authorAvatar: './assets/default-avatar.jpg',
        authorLink: '#',
        publishTime: '',
        link: '#',
        analysis: '暂无分析内容',
        likes: '0',
        comments: '0',
        favorites: '0',
        shares: '0',
        topics: [],
        status: 'unknown'
      };
      
      // 更新标题
      document.getElementById('drawer-title').textContent = `素材详情`;
      
      // 更新素材基本信息
      document.getElementById('material-title').textContent = material.title;
      document.getElementById('material-author').textContent = material.author;
      document.getElementById('material-author-avatar').src = material.authorAvatar;
      document.getElementById('material-publish-time').textContent = `发布时间：${material.publishTime}`;
      
      // 更新作者信息和链接
      document.getElementById('material-author-link').href = material.authorLink;
      document.getElementById('material-author-profile').href = material.authorLink;
      
      // 更新互动数据
      document.querySelector('.flex.items-center.gap-2.ml-3 div:nth-child(1) span').textContent = material.likes || '0';
      document.querySelector('.flex.items-center.gap-2.ml-3 div:nth-child(2) span').textContent = material.comments || '0';
      document.querySelector('.flex.items-center.gap-2.ml-3 div:nth-child(3) span').textContent = material.favorites || '0';
      document.querySelector('.flex.items-center.gap-2.ml-3 div:nth-child(4) span').textContent = material.shares || '0';
      
      // 更新链接
      document.getElementById('material-link-cover').href = material.link;
      document.getElementById('material-link-title').href = material.link;
      
      // 更新视频标签、脚本类型和爆款元素
      updateMaterialMetadata(material);
      
      // 更新封面图
      const coverContainer = document.getElementById('material-cover-container');
      
      if (material.cover) {
        coverContainer.innerHTML = `
          <a href="${material.link}" target="_blank">
            <img src="${material.cover}" alt="视频封面" class="w-full h-full object-cover" />
          </a>
        `;
      } else {
        coverContainer.innerHTML = `
          <a href="${material.link}" target="_blank">
            <div class="w-full h-full flex items-center justify-center bg-base-200 text-base-content opacity-50">
              <span class="text-sm">无封面</span>
            </div>
          </a>
        `;
      }
      
      // 根据分析状态显示不同内容
      const analysisContent = document.getElementById('material-analysis-content');
      
      if (material.status === 'success' && material.analysis) {
        // 将纯文本转换为HTML
        analysisContent.innerHTML = material.analysis.replace(/\n/g, '<br>');
      } else if (material.status === 'processing') {
        analysisContent.innerHTML = `
          <div class="flex justify-center items-center py-8">
            <span class="loading loading-spinner loading-md text-warning"></span>
            <span class="ml-2">分析处理中...</span>
          </div>
        `;
      } else if (material.status === 'pending') {
        analysisContent.innerHTML = `
          <div class="flex justify-center items-center py-8">
            <span class="loading loading-spinner loading-md"></span>
            <span class="ml-2">等待分析...</span>
          </div>
        `;
      } else if (material.status === 'error') {
        analysisContent.innerHTML = `
          <div class="flex justify-center items-center py-8 text-error">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <span>${material.errorMessage || '分析失败，请重试'}</span>
          </div>
        `;
      } else {
        analysisContent.innerHTML = `
          <div class="flex justify-center items-center py-8 opacity-50">
            <span>暂无分析内容</span>
          </div>
        `;
      }
      
      // 加载关联选题
      loadRelatedTopics(material);
      
      // 更新分析流程步骤状态
      updateAnalysisSteps(material.status);
    }
    
    // 加载关联选题
    function loadRelatedTopics(material) {
      const topicsContainer = document.getElementById('topics-container');
      const topicsLoading = document.getElementById('topics-loading');
      const topicsEmpty = document.getElementById('topics-empty');
      const topicsError = document.getElementById('topics-error');
      const topicsTitle = document.getElementById('topics-title');
      
      // 隐藏所有状态
      topicsContainer.innerHTML = '';
      topicsLoading.classList.add('hidden');
      topicsEmpty.classList.add('hidden');
      topicsError.classList.add('hidden');
      
      // 根据素材状态显示不同内容
      if (material.status === 'success' && material.topics && material.topics.length > 0) {
        // 更新标题显示选题数量
        topicsTitle.textContent = `已为你生成 ${material.topics.length} 条选题`;
        
        // 显示选题列表
        material.topics.forEach((topic, index) => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-base-200 transition-colors duration-200';
          
          const confirmBtnClass = topic.status === 'confirmed' 
            ? 'btn-dash btn-success' 
            : 'btn-outline';
          
          const confirmBtnText = topic.status === 'confirmed' 
            ? '已确认' 
            : '确认选题';
          
          row.innerHTML = `
            <td>${index + 1}</td>
            <td>
              <div>${topic.title}</div>
              <div class="text-xs opacity-60 mt-1 block md:hidden">
                <span>${topic.scriptType}</span>
                <span class="mx-2">•</span>
                <span>${topic.date}</span>
              </div>
            </td>
            <td class="hidden md:table-cell">${topic.scriptType}</td>
            <td class="hidden md:table-cell">${topic.date}</td>
            <td>
              <button class="btn btn-xs ${confirmBtnClass} table-action-btn confirm-topic" 
                data-topic-id="${topic.id}" 
                onclick="confirmTopic('${topic.id}')">
                ${confirmBtnText}
              </button>
            </td>
          `;
          
          topicsContainer.appendChild(row);
        });
      } else if (material.status === 'analyzing') {
        // 更新标题为分析中状态
        topicsTitle.textContent = '正在分析素材...';
        
        // 显示分析中
        topicsLoading.classList.remove('hidden');
        topicsLoading.innerHTML = `
          <div class="flex justify-center items-center py-8">
            <span class="loading loading-spinner loading-md text-warning"></span>
            <span class="ml-2">素材分析中，完成后将自动生成选题...</span>
          </div>
        `;
      } else if (material.status === 'dissecting') {
        // 更新标题为拆片中状态
        topicsTitle.textContent = '正在拆解素材...';
        
        // 显示拆片中
        topicsLoading.classList.remove('hidden');
        topicsLoading.innerHTML = `
          <div class="flex justify-center items-center py-8">
            <span class="loading loading-spinner loading-md text-info"></span>
            <span class="ml-2">素材拆片中，完成后将生成选题...</span>
          </div>
        `;
      } else if (material.status === 'generating') {
        // 更新标题为生成中状态
        topicsTitle.textContent = '正在为你生成选题...';
        
        // 显示加载中
        topicsLoading.classList.remove('hidden');
        topicsLoading.innerHTML = `
          <div class="flex justify-center items-center py-8">
            <span class="loading loading-spinner loading-md text-success"></span>
            <span class="ml-2">选题生成中，请稍候...</span>
          </div>
        `;
      } else if (material.status === 'pending') {
        // 更新标题为待处理状态
        topicsTitle.textContent = '等待开始处理';
        
        // 显示待处理状态
        topicsEmpty.classList.remove('hidden');
        topicsEmpty.innerHTML = `
          <div class="flex justify-center items-center py-8 text-base-content opacity-60">
            <span class="loading loading-spinner loading-md mr-2"></span>
            <span>素材待处理，开始处理后将生成选题</span>
          </div>
        `;
      } else if (material.status === 'error') {
        // 更新标题为错误状态
        topicsTitle.textContent = '选题生成失败';
        
        // 显示错误信息
        document.getElementById('topics-error-message').textContent = material.errorMessage || '加载失败，请稍后再试';
        topicsError.classList.remove('hidden');
      } else {
        // 更新标题为空状态
        topicsTitle.textContent = '暂无选题';
        
        // 显示空状态
        topicsEmpty.classList.remove('hidden');
      }
    }
    
    // 根据素材状态切换按钮可见性
    function toggleActionButtons(materialId) {
      const generateBtn = document.getElementById('btn-generate-topics');
      
      // 根据素材状态设置按钮状态
      const materials = {
        'M001': { status: 'success' },
        'M002': { status: 'analyzing' },
        'M003': { status: 'success' },
        'M004': { status: 'pending' },
        'M005': { status: 'dissecting' },
        'M006': { status: 'generating' }
      };
      
      const material = materials[materialId] || { status: 'unknown' };
      
      // 根据状态设置按钮可见性
      if (material.status === 'success') {
        // 处理完成，可以生成更多选题
        generateBtn.classList.remove('hidden');
        generateBtn.textContent = '再次生成';
        generateBtn.onclick = function() { openMoreTopicsModal(materialId); };
      } else if (material.status === 'pending') {
        // 待处理状态，显示开始处理按钮
        generateBtn.classList.remove('hidden');
        generateBtn.textContent = '开始处理';
        generateBtn.onclick = function() { startProcessing(materialId); };
      } else if (material.status === 'dissecting' || material.status === 'analyzing' || material.status === 'generating' || material.status === 'error') {
        // 处理中的各个状态或出错状态，不显示按钮
        generateBtn.classList.add('hidden');
      } else {
        // 未知状态，不显示按钮
        generateBtn.classList.add('hidden');
      }
    }
    
    // 开始处理素材
    function startProcessing(materialId) {
      // 实际应用中这里会调用API开始处理素材
      alert('开始处理素材: ' + materialId);
      // 关闭抽屉
      document.getElementById('drawer-detail').checked = false;
    }
    
    // 初始化确认选题按钮
    function initConfirmTopicButtons() {
      const confirmButtons = document.querySelectorAll('.confirm-topic');
      
      confirmButtons.forEach(button => {
        // 初始化数据属性
        const isConfirmed = button.textContent.trim() === '已确认';
        button.setAttribute('data-confirmed', isConfirmed ? 'true' : 'false');
        
        button.addEventListener('click', function(event) {
          event.stopPropagation(); // 阻止事件冒泡
          
          const topicId = this.getAttribute('data-topic-id');
          confirmTopic(topicId);
        });
      });
    }
    
    // 确认选题（从抽屉中调用）
    function confirmTopic(topicId) {
      // 查找对应的按钮
      const button = document.querySelector(`.confirm-topic[data-topic-id="${topicId}"]`);
      
      if (button) {
        // 检查当前状态，使用数据属性而不是文本内容
        const isConfirmed = button.getAttribute('data-confirmed') === 'true';
        
        if (isConfirmed) {
          // 如果已确认，则显示撤销确认对话框
          document.getElementById('cancel-topic-id').value = topicId;
          document.getElementById('cancel_topic_modal').showModal();
        } else {
          // 如果未确认，则进行确认
          // 保存当前选题ID
          document.getElementById('confirm-topic-id').value = topicId;
          
          // 显示确认对话框
          document.getElementById('confirm_topic_modal').showModal();
        }
      }
    }
    
    // URL模态框函数
    function openUrlModal(id) {
      const urlContent = document.getElementById('url-content');
      const urlModal = document.getElementById('url-modal');
      
      // 根据ID获取链接内容（这里使用示例数据）
      const urls = {
        'M001': 'https://www.douyin.com/video/7123456789012345678',
        'M002': 'https://www.douyin.com/video/7234567890123456789',
        'M003': 'https://www.douyin.com/video/7345678901234567890',
        'M004': 'https://www.douyin.com/video/7456789012345678901',
        'M005': 'https://www.douyin.com/video/7567890123456789012',
        'M006': 'https://www.douyin.com/video/7678901234567890123'
      };
      
      urlContent.textContent = urls[id] || '链接不存在';
      urlModal.showModal();
    }
    
    // 素材分析结果模态框函数
    function openTextModal(id) {
      // 打开右侧抽屉替代模态框
      openDetailDrawer(id);
    }
    
    // "再来五条"按钮函数
    function openMoreTopicsModal(id) {
      const materialTitles = {
        'M001': '新房装修完，怎么除甲醛？',
        'M002': '《欣赏优秀视频之强记女工，全女维修团队真的太棒了》',
        'M003': '如何提高工作效率？5个实用小技巧',
        'M004': '这些摄影小技巧让你的照片更专业',
        'M005': '三款超适合夏天的清爽饮品，简单易做',
        'M006': '家庭整理收纳的10个小窍门',
        'M007': '5分钟学会Excel高效办公技巧',
        'M008': '数据分析入门指南：从零开始学习数据分析'
      };
      
      // 设置参考素材标题和ID
      document.getElementById('reference-material').value = materialTitles[id] || '';
      document.getElementById('current-material-id').value = id;
      
      // 显示模态框
      document.getElementById('more-topics-modal').showModal();
    }
    
    // 添加素材函数
    function addMaterial() {
      // 获取输入的链接内容
      const textarea = document.querySelector('#add_material_modal textarea');
      const link = textarea.value.trim();
      
      if (!link) {
        showToast('请输入抖音视频链接', 'warning');
        return;
      }
      
      // 这里应该调用API添加素材
      // 现在只是模拟成功
      showToast('素材已添加，开始分析处理', 'success');
      
      // 清空输入框
      textarea.value = '';
      
      // 关闭modal
      document.getElementById('add_material_modal').close();
    }
    
    // 生成更多选题的函数
    function generateMoreTopics() {
      // 获取当前操作的素材ID
      const materialId = document.getElementById('current-material-id').value;
      
      // 显示选题生成中状态（在表格上方）
      document.getElementById('topics-generating').classList.remove('hidden');
      
      // 在实际应用中，这里会调用API生成更多选题
      // 关闭模态框
      document.getElementById('more-topics-modal').close();
      
      // 更新选题生成状态为"进行中"
      updateTopicGenerationStatus(materialId);
      
      // 模拟生成完成（实际应用中由API回调处理）
      setTimeout(() => {
        // 隐藏loading状态
        document.getElementById('topics-generating').classList.add('hidden');
        
        // 添加新生成的选题到表格中（添加到开头）
        const topicsContainer = document.getElementById('topics-container');
        const newTopics = [
          {
            id: 'T006',
            title: '装修甲醛检测：专业vs自测，哪种方法更准确？',
            scriptType: '对比分析型脚本',
            date: getCurrentDate()
          },
          {
            id: 'T007',
            title: '甲醛释放周期真相：为什么通风三个月还不够？',
            scriptType: '科普揭秘型脚本',
            date: getCurrentDate()
          },
          {
            id: 'T008',
            title: '新房除甲醛的5个误区，99%的人都中招了',
            scriptType: '避坑指南型脚本',
            date: getCurrentDate()
          },
          {
            id: 'T009',
            title: '除甲醛产品大测评：哪款性价比最高？',
            scriptType: '测评型脚本',
            date: getCurrentDate()
          },
          {
            id: 'T010',
            title: '入住新房前的甲醛检测流程，一步不能少',
            scriptType: '流程指导型脚本',
            date: getCurrentDate()
          }
        ];
        
        // 将新选题插入到表格开头
        newTopics.reverse().forEach((topic) => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-base-200 transition-colors duration-200';
          row.innerHTML = `
            <td>1</td>
            <td>
              <div>${topic.title}</div>
              <div class="text-xs opacity-60 mt-1 block md:hidden">
                <span>${topic.scriptType}</span>
                <span class="mx-2">•</span>
                <span>${topic.date}</span>
              </div>
            </td>
            <td class="hidden md:table-cell">${topic.scriptType}</td>
            <td class="hidden md:table-cell">${topic.date}</td>
            <td>
              <button class="btn btn-xs btn-outline table-action-btn confirm-topic" data-topic-id="${topic.id}">确认选题</button>
            </td>
          `;
          // 插入到表格开头
          topicsContainer.insertBefore(row, topicsContainer.firstChild);
        });
        
        // 重新计算所有选题的序号
        const allRows = topicsContainer.querySelectorAll('tr');
        allRows.forEach((row, index) => {
          row.querySelector('td:first-child').textContent = index + 1;
        });
        
        // 重新初始化确认选题按钮
        initConfirmTopicButtons();
        
        // 更新标题显示新的总数
        document.getElementById('topics-title').textContent = `已为你生成 ${allRows.length} 条选题`;
        
        // 显示完成提示
        showToast('选题生成完成，已增加5条新选题', 'success');
      }, 3000); // 3秒后模拟完成
      
      // 显示生成开始提示
      showToast('已开始生成更多选题', 'info');
    }
    
    // 获取当前日期的辅助函数
    function getCurrentDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    
    // 更新选题生成状态
    function updateTopicGenerationStatus(materialId) {
      // 查找对应素材行的选题生成状态单元格
      const row = document.querySelector(`tr[data-material-id="${materialId}"]`);
      if (row) {
        const statusCell = row.querySelector('td:nth-child(5)'); // 选题生成状态列
        if (statusCell) {
          // 更新为"进行中"状态
          statusCell.innerHTML = `
            <div class="flex items-center gap-1">
              <div class="inline-grid *:[grid-area:1/1]">
                <div class="status status-warning animate-ping"></div>
                <div class="status status-warning"></div>
              </div>
              <span>生成选题中</span>
              </div>
            `;
        }
      }
    }
    
    // 更新侧边栏数量
    function updateSidebarCounts() {
      // 计算素材总数
      const materialCount = document.querySelectorAll('tr[data-material-id]').length;
      
      // 计算已确认选题总数
      // 在实际应用中，这里应该使用API获取或从存储中获取
      // 这里使用模拟数据
      const topicCount = 5; // 示例数据
      
      // 更新侧边栏徽章
      const materialsBadge = document.querySelector('a[href="assets.html"] .badge');
      const topicsBadge = document.querySelector('a[href="topics.html"] .badge');
      
      if (materialsBadge) {
        materialsBadge.textContent = materialCount;
      }
      
      if (topicsBadge) {
        topicsBadge.textContent = topicCount;
      }
    }
    
    // 最终确认选题
    function confirmTopicFinal() {
      // 获取选题ID
      const topicId = document.getElementById('confirm-topic-id').value;
      
      // 在抽屉中查找对应的按钮
      const button = document.querySelector(`.confirm-topic[data-topic-id="${topicId}"]`);
      
      if (button) {
        // 切换按钮状态为已确认
        button.textContent = '已确认';
        button.classList.remove('btn-outline');
        button.classList.add('btn-dash', 'btn-success');
        button.setAttribute('data-confirmed', 'true');
        
        // 显示成功提示
        showToast('已添加到选题库，开始生成文案', 'success');
      }
      
      // 关闭确认对话框
      document.getElementById('confirm_topic_modal').close();
    }
    
    // 最终撤销选题
    function cancelTopicFinal() {
      // 获取选题ID
      const topicId = document.getElementById('cancel-topic-id').value;
      
      // 在抽屉中查找对应的按钮
      const button = document.querySelector(`.confirm-topic[data-topic-id="${topicId}"]`);
      
      if (button) {
        // 切换按钮状态为未确认
        button.textContent = '确认选题';
        button.classList.remove('btn-dash', 'btn-success');
        button.classList.add('btn-outline');
        button.setAttribute('data-confirmed', 'false');
        
        // 显示撤销成功提示
        showToast('已撤销选题确认', 'warning');
      }
      
      // 关闭撤销确认对话框
      document.getElementById('cancel_topic_modal').close();
    }
    
    // 显示Toast提示
    function showToast(message, type = 'success') {
      // 定义不同类型的Toast配置
      const toastConfigs = {
        success: {
          alertClass: 'alert-success',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>`
        },
        info: {
          alertClass: 'alert-info',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="h-6 w-6 shrink-0 stroke-current">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
          </svg>`
        },
        warning: {
          alertClass: 'alert-warning',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>`
        },
        error: {
          alertClass: 'alert-error',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>`
        }
      };
      
      const config = toastConfigs[type] || toastConfigs.success;
      
      // 创建Toast元素
      const toastContainer = document.createElement('div');
      toastContainer.className = 'toast toast-top toast-end';
      toastContainer.innerHTML = `
        <div role="alert" class="alert ${config.alertClass}">
          ${config.icon}
          <span>${message}</span>
        </div>
      `;
      
      // 添加到页面
      document.body.appendChild(toastContainer);
      
      // 显示动画
      setTimeout(() => {
        toastContainer.style.opacity = '1';
      }, 10);
      
      // 3秒后移除
      setTimeout(() => {
        toastContainer.style.opacity = '0';
        setTimeout(() => {
          if (toastContainer.parentNode) {
            document.body.removeChild(toastContainer);
          }
        }, 300);
      }, 3000);
    }
    
    // 更新视频标签、脚本类型和爆款元素
    function updateMaterialMetadata(material) {
      // 更新脚本类型
      document.getElementById('material-script-type').textContent = material.scriptType || '-';
      
      // 更新视频标签
      const tagsContainer = document.getElementById('material-tags');
      tagsContainer.innerHTML = '';
      
      if (material.tags && material.tags.length > 0) {
        material.tags.forEach(tag => {
          const tagSpan = document.createElement('span');
          tagSpan.className = 'tag-item';
          tagSpan.textContent = tag;
          tagsContainer.appendChild(tagSpan);
        });
      } else {
        tagsContainer.innerHTML = '<span class="opacity-50">-</span>';
      }
      
      // 更新爆款元素
      const elementsContainer = document.getElementById('material-elements');
      elementsContainer.innerHTML = '';
      
      if (material.elements && material.elements.length > 0) {
        material.elements.forEach(element => {
          const elementSpan = document.createElement('span');
          elementSpan.className = 'tag-item';
          elementSpan.textContent = element;
          elementsContainer.appendChild(elementSpan);
        });
      } else {
        elementsContainer.innerHTML = '<span class="opacity-50">-</span>';
      }
    }
    
    // 更新分析流程步骤状态
    function updateAnalysisSteps(status) {
      const steps = document.querySelectorAll('.timeline li');
      
      // 重置所有步骤状态
      steps.forEach(step => {
        const icon = step.querySelector('.timeline-middle svg');
        if (icon) {
          icon.className = 'h-5 w-5 text-base-content opacity-30';
        }
      });
      
      // 根据状态设置完成的步骤
      switch(status) {
        case 'success':
          // 全部完成
          steps.forEach(step => {
            const icon = step.querySelector('.timeline-middle svg');
            if (icon) {
              icon.className = 'h-5 w-5 text-neutral';
            }
          });
          break;
        case 'generating':
          // 前三步完成，第四步进行中
          for (let i = 0; i < 3; i++) {
            const icon = steps[i]?.querySelector('.timeline-middle svg');
            if (icon) {
              icon.className = 'h-5 w-5 text-neutral';
            }
          }
          const generatingIcon = steps[3]?.querySelector('.timeline-middle svg');
          if (generatingIcon) {
            generatingIcon.className = 'h-5 w-5 text-success';
          }
          break;
        case 'analyzing':
          // 第一步完成，第二步进行中
          const firstIcon = steps[0]?.querySelector('.timeline-middle svg');
          if (firstIcon) {
            firstIcon.className = 'h-5 w-5 text-neutral';
          }
          const analyzingIcon = steps[1]?.querySelector('.timeline-middle svg');
          if (analyzingIcon) {
            analyzingIcon.className = 'h-5 w-5 text-warning';
          }
          break;
        case 'dissecting':
          // 第一步进行中
          const dissectingIcon = steps[0]?.querySelector('.timeline-middle svg');
          if (dissectingIcon) {
            dissectingIcon.className = 'h-5 w-5 text-info';
          }
          break;
        case 'pending':
        default:
          // 所有步骤都未完成，保持默认状态
          break;
      }
    }
    
    // 打开素材分析结果Modal
    function openAnalysisModal(materialId) {
      // 获取分析结果数据（示例数据，实际应用中应从服务器获取）
      const analysisData = {
        'M001': `# 新房除甲醛视频分析报告

## 📊 视频内容概述

这是一个关于新房除甲醛的**教学视频**，采用解体型知识传授脚本。作者以专业的角度介绍了5种有效的除甲醛方法，内容实用性强，对准备装修或刚装修完的用户具有很高的参考价值。

### 核心信息
- **视频类型**: 知识教学类
- **目标受众**: 装修用户群体
- **内容价值**: 实用性强，解决刚需问题
- **专业度**: 高，具有权威性

## 🎯 脚本结构分析

视频采用经典的**"问题-解决方案"**结构：

1. **开头hook** - 提出除甲醛的需求痛点
2. **核心内容** - 依次介绍5种除甲醛方法：
   - 通风换气法
   - 活性炭吸附法
   - 植物净化法
   - 专业除醛产品
   - 空气净化器
3. **操作指导** - 每种方法都有具体的操作指导
4. **效果对比** - 结尾强调效果对比，增强可信度

> 💡 **结构亮点**: 循序渐进的介绍方式，从免费到付费方案，满足不同用户需求

## ⚡ 爆款元素识别

### 权威性元素
- **专业身份**: 作者以装修专家身份出镜，增加可信度
- **专业术语**: 使用专业的装修和除醛术语
- **科学依据**: 提到甲醛的危害和检测标准

### 实用性元素
- **具体方法**: 提供5种具体可行的方法
- **成本分析**: 从免费到付费方案的成本对比
- **效果评估**: 对每种方法的效果进行客观评价

### 传播性元素
- **对比突出**: 与传统通风方法对比，突出优势
- **视觉化展示**: 通过实物演示增强说服力
- **结果导向**: 强调实际效果，增加分享动力

## 💰 变现潜力评估

### 综合评分: ⭐⭐⭐⭐☆ (4/5)

| 评估维度 | 得分 | 分析说明 |
|---------|------|----------|
| **市场需求** | ⭐⭐⭐⭐⭐ | 装修除甲醛是刚需，市场需求大 |
| **用户画像** | ⭐⭐⭐⭐⭐ | 目标明确：准备装修、正在装修、刚装修完 |
| **转化意愿** | ⭐⭐⭐⭐⭐ | 健康相关，用户付费意愿强 |
| **内容质量** | ⭐⭐⭐⭐☆ | 内容实用，但竞争激烈 |
| **传播性** | ⭐⭐⭐☆☆ | 实用但话题性一般 |

### 变现方式推荐

1. **产品推广** (推荐指数: ⭐⭐⭐⭐⭐)
   - 除甲醛产品（活性炭、净化器）
   - 检测仪器和试剂
   - 专业除醛服务

2. **知识付费** (推荐指数: ⭐⭐⭐⭐☆)
   - 装修避坑课程
   - 一对一装修咨询
   - 装修资料包

3. **服务导流** (推荐指数: ⭐⭐⭐☆☆)
   - 装修公司合作
   - 检测机构推荐
   - 保险产品销售

### 优化建议

- **增加互动**: 添加用户提问和解答环节
- **案例丰富**: 增加更多真实案例分享
- **工具推荐**: 详细介绍推荐的产品和工具
- **后续跟踪**: 制作系列内容，形成完整的装修指南`,
        'M003': `# 工作效率提升视频分析报告

## 📊 视频内容概述

这是一个关于**提高工作效率**的教学视频，通过5个实用小技巧帮助观众优化工作流程。内容针对职场人群的痛点，具有很强的实用性和传播性。

### 核心信息
- **视频类型**: 职场技能分享
- **目标受众**: 职场人士、学生群体
- **内容价值**: 立即可用的效率技巧
- **适用性**: 广泛适用于各种工作场景

## 🎯 脚本结构分析

采用**清单式结构**，逐一介绍技巧：

### 结构框架
1. **痛点引入** - 开头点出工作效率问题
2. **技巧展示** - 依次展示5个小技巧：
   - 番茄工作法
   - 任务优先级管理
   - 快捷键使用
   - 时间块规划
   - 干扰源消除
3. **案例说明** - 每个技巧都有具体示例
4. **行动呼吁** - 结尾总结，鼓励实践

> 💡 **结构特点**: 节奏紧凑，信息密度高，观看体验好

## ⚡ 爆款元素识别

### 痛点导向
- **切中要害**: 直击职场效率低下问题
- **共鸣强烈**: 大部分职场人都有的困扰
- **解决方案**: 提供立即可行的解决办法

### 实用性强
- **技巧实用**: 5个技巧都简单易实施
- **成本低廉**: 大部分技巧无需额外投入
- **效果明显**: 能够产生立竿见影的效果

### 传播价值
- **案例丰富**: 每个技巧都有具体应用场景
- **可分享性**: 内容有价值，值得分享给同事朋友
- **实用主义**: 注重实际应用而非理论说教

## 💰 变现潜力评估

### 综合评分: ⭐⭐⭐☆☆ (3/5)

| 评估维度 | 得分 | 分析说明 |
|---------|------|----------|
| **市场需求** | ⭐⭐⭐⭐☆ | 效率提升是普遍需求 |
| **用户画像** | ⭐⭐⭐⭐☆ | 职场人士、学生群体广泛 |
| **转化意愿** | ⭐⭐⭐☆☆ | 免费内容较多，付费意愿中等 |
| **内容质量** | ⭐⭐⭐⭐☆ | 实用性强，但同质化内容多 |
| **传播性** | ⭐⭐⭐⭐☆ | 有分享价值，传播性好 |

### 变现方式推荐

1. **工具推广** (推荐指数: ⭐⭐⭐⭐☆)
   - 效率类软件（notion、滴答清单）
   - 硬件设备（机械键盘、显示器）
   - 办公用品（笔记本、文具）

2. **课程销售** (推荐指数: ⭐⭐⭐☆☆)
   - 时间管理系统课程
   - 职场技能提升课程
   - 个人效率训练营

3. **咨询服务** (推荐指数: ⭐⭐☆☆☆)
   - 一对一效率诊断
   - 企业培训服务
   - 职业规划咨询

### 内容升级建议

- **深度化**: 针对特定行业提供专业化解决方案
- **系统化**: 构建完整的效率提升体系
- **个性化**: 根据不同用户类型定制内容
- **互动化**: 增加用户参与和反馈环节`,
        // 默认分析结果
        'default': `# 素材分析中...

## 📊 分析状态

该素材正在进行深度分析，请稍后查看完整报告。

### 分析进度
- [x] 视频结构识别
- [x] 内容主题提取  
- [ ] 脚本类型分析
- [ ] 爆款元素识别
- [ ] 变现潜力评估

> ⏰ **预计完成时间**: 3-5分钟

---

*分析完成后将自动更新内容*`
      };
      
      // 获取当前素材的分析数据
      const markdownContent = analysisData[materialId] || analysisData['default'];
      
      // 使用marked.js渲染Markdown
      const htmlContent = marked.parse(markdownContent);
      
      // 更新Modal内容
      const analysisContent = document.getElementById('analysis-content');
      analysisContent.innerHTML = htmlContent;
      
      // 显示Modal
      document.getElementById('analysis-modal').showModal();
    }
  </script>
  
  
  <!-- 素材详情抽屉 -->
  <div class="drawer drawer-end">
    <input id="drawer-detail" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content">
      <!-- 页面内容，抽屉关闭时显示 -->
    </div>
    <div class="drawer-side z-50">
      <label for="drawer-detail" aria-label="关闭详情" class="drawer-overlay"></label>
      <div class="bg-base-100 min-h-full w-full md:w-[768px] p-6 overflow-y-auto" role="dialog" aria-modal="true" aria-labelledby="drawer-title">
        <!-- 抽屉标题 -->
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-bold" id="drawer-title">素材详情</h3>
          <label for="drawer-detail" class="btn btn-sm btn-ghost" aria-label="关闭详情抽屉 (ESC)">
            <i class="fas fa-times"></i>
          </label>
        </div>
        
        <!-- 素材基本信息 -->
        <div class="mb-6" id="material-info">
          <div class="flex gap-4 mb-4">
            <div class="w-24 h-40 rounded-lg bg-base-200 overflow-hidden" id="material-cover-container">
              <a href="#" id="material-link-cover" target="_blank" class="block relative w-full h-full">
                <img src="" alt="视频封面" id="material-cover" class="w-full h-full object-cover" />
              </a>
            </div>
            <div class="flex-1">
              <a href="#" id="material-link-title" target="_blank">
                <h4 class="text-base font-medium mb-2" id="material-title"></h4>
              </a>

              <div class="flex items-center mb-3">
                <div class="avatar mr-2">
                  <a href="#" id="material-author-link" target="_blank">
                    <div class="w-6 h-6 rounded-full">
                      <img src="" id="material-author-avatar" alt="作者头像" />
                    </div>
                  </a>
                </div>
                <a href="#" id="material-author-profile" target="_blank" class="hover:underline">
                  <span class="text-sm" id="material-author"></span>
                </a>
                <!-- 互动数据 -->
                <div class="flex items-center gap-2 ml-3 text-sm opacity-60">
                  <div class="flex items-center gap-1">
                    <i class="fas fa-heart"></i>
                    <span>3.6万</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <i class="fas fa-comment"></i>
                    <span>5636</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <i class="fas fa-star"></i>
                    <span>2235</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <i class="fas fa-share"></i>
                    <span>8596</span>
                  </div>
                </div>
              </div>
              <div class="text-sm opacity-60 mb-3" id="material-publish-time"></div>
              
              <!-- 视频标签、脚本类型和爆款元素 -->
              <div class="space-y-2 text-sm opacity-60">
                <div class="flex">
                  <span class="w-20 flex-shrink-0">视频标签：</span>
                  <div class="tag-container" id="material-tags">
                    <!-- 标签内容将通过JavaScript动态加载 -->
                  </div>
                </div>
                <div class="flex">
                  <span class="w-20 flex-shrink-0">脚本类型：</span>
                  <span id="material-script-type">-</span>
                </div>
                <div class="flex">
                  <span class="w-20 flex-shrink-0">爆款元素：</span>
                  <div class="tag-container" id="material-elements">
                    <!-- 爆款元素将通过JavaScript动态加载 -->
                  </div>
                </div>
                <div class="flex">
                  <span class="w-20 flex-shrink-0">分析结果：</span>
                  <button class="link hover:underline text-sm" onclick="openAnalysisModal(currentMaterialId)">查看素材分析结果</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 雪碧图 -->
        <div class="mb-6">
          <div class="w-full bg-black">
            <a href="https://vod.qihaozhushou.com/tos-vod-cn-v-d2fb25c7b0ded72b/8a4f643633174cf1b433991b6075449f~noop.image" target="_blank"><img src="https://vod.qihaozhushou.com/tos-vod-cn-v-d2fb25c7b0ded72b/8a4f643633174cf1b433991b6075449f~noop.image" alt="视频互动雪碧图" class="w-full"></a>
          </div>
        </div>
        
        <!-- 素材分析流程 -->
        <div class="mb-6">
          <ul class="timeline md:timeline-horizontal timeline-vertical">
            <li>
              <div class="timeline-start timeline-box">完成视频结构理解和拆片</div>
              <div class="timeline-middle">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-neutral">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"/>
                </svg>
              </div>
              <hr/>
            </li>
            <li>
              <hr/>
              <div class="timeline-middle">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-neutral">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="timeline-end timeline-box">完成素材分析、脚本类型和爆款元素分析</div>
              <hr/>
            </li>
            <li>
              <hr/>
              <div class="timeline-start timeline-box">引用商业定位和分析结果</div>
              <div class="timeline-middle">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-neutral">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"/>
                </svg>
              </div>
              <hr/>
            </li>
            <li>
              <hr/>
              <div class="timeline-middle">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-neutral">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="timeline-end timeline-box">生成爆款选题</div>
            </li>
          </ul>
        </div>
        
        <!-- 为你生成的选题 -->
        <div>
          <h4 class="text-lg font-semibold mb-4" id="topics-title">已为你生成 5 条选题</h4>
          
          <div id="material-topics">
            <!-- 选题生成中状态 - 在表格上方 -->
            <div id="topics-generating" class="hidden mb-4">
              <div class="flex items-center justify-center py-6 border border-base-300 rounded-lg bg-base-50">
                <span class="loading loading-spinner loading-md text-primary mr-3"></span>
                <span class="text-base-content">正在为你生成更多选题...</span>
              </div>
            </div>
            
            <div class="overflow-x-auto">
              <table class="table bg-base-100 w-full">
                <thead>
                  <tr class="border-b border-base-300">
                    <th class="text-sm opacity-60 w-8">#</th>
                    <th class="text-sm opacity-60">选题标题</th>
                    <th class="text-sm opacity-60 min-w-[100px] hidden md:table-cell">脚本类型</th>
                    <th class="text-sm opacity-60 min-w-[80px] hidden md:table-cell">生成时间</th>
                    <th class="text-sm opacity-60 min-w-[80px]">操作</th>
                  </tr>
                </thead>
                <tbody id="topics-container">
                  <!-- 选题列表示例 -->
                  <tr class="hover:bg-base-200 transition-colors duration-200">
                    <td>1</td>
                    <td>
                      <div>新房装修除甲醛最有效的5种方法，第3种最省钱!</div>
                      <div class="text-xs opacity-60 mt-1 block md:hidden">
                        <span>干货型脚本</span>
                        <span class="mx-2">•</span>
                        <span>2023-11-25</span>
                      </div>
                    </td>
                    <td class="hidden md:table-cell">干货型脚本</td>
                    <td class="hidden md:table-cell">2023-11-25</td>
                    <td>
                      <button class="btn btn-xs btn-outline table-action-btn confirm-topic" data-topic-id="T001">确认选题</button>
                    </td>
                  </tr>
                  <tr class="hover:bg-base-200 transition-colors duration-200">
                    <td>2</td>
                    <td>
                      <div>新房入住前必做的除甲醛步骤，专家建议必看</div>
                      <div class="text-xs opacity-60 mt-1 block md:hidden">
                        <span>解决方案类脚本</span>
                        <span class="mx-2">•</span>
                        <span>2023-11-25</span>
                      </div>
                    </td>
                    <td class="hidden md:table-cell">解决方案类脚本</td>
                    <td class="hidden md:table-cell">2023-11-25</td>
                    <td>
                      <button class="btn btn-xs btn-dash btn-success table-action-btn confirm-topic" data-topic-id="T002">已确认</button>
                    </td>
                  </tr>
                  <tr class="hover:bg-base-200 transition-colors duration-200">
                    <td>3</td>
                    <td>
                      <div>装修后甲醛超标怎么办？专业除醛指南</div>
                      <div class="text-xs opacity-60 mt-1 block md:hidden">
                        <span>问题解决型脚本</span>
                        <span class="mx-2">•</span>
                        <span>2023-11-24</span>
                      </div>
                    </td>
                    <td class="hidden md:table-cell">问题解决型脚本</td>
                    <td class="hidden md:table-cell">2023-11-24</td>
                    <td>
                      <button class="btn btn-xs btn-dash btn-success table-action-btn confirm-topic" data-topic-id="T004">已确认</button>
                    </td>
                  </tr>
                  <tr class="hover:bg-base-200 transition-colors duration-200">
                    <td>4</td>
                    <td>
                      <div>除甲醛最有效的植物有哪些？这5种植物效果最好</div>
                      <div class="text-xs opacity-60 mt-1 block md:hidden">
                        <span>榜单类脚本</span>
                        <span class="mx-2">•</span>
                        <span>2023-11-24</span>
                      </div>
                    </td>
                    <td class="hidden md:table-cell">榜单类脚本</td>
                    <td class="hidden md:table-cell">2023-11-24</td>
                    <td>
                      <button class="btn btn-xs btn-outline table-action-btn confirm-topic" data-topic-id="T003">确认选题</button>
                    </td>
                  </tr>
                  <tr class="hover:bg-base-200 transition-colors duration-200">
                    <td>5</td>
                    <td>
                      <div>别再被骗了！这些除甲醛方法根本不管用</div>
                      <div class="text-xs opacity-60 mt-1 block md:hidden">
                        <span>揭秘型脚本</span>
                        <span class="mx-2">•</span>
                        <span>2023-11-23</span>
                      </div>
                    </td>
                    <td class="hidden md:table-cell">揭秘型脚本</td>
                    <td class="hidden md:table-cell">2023-11-23</td>
                    <td>
                      <button class="btn btn-xs btn-outline table-action-btn confirm-topic" data-topic-id="T005">确认选题</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <div id="topics-loading" class="flex justify-center items-center py-8 hidden">
            <span class="loading loading-spinner loading-md"></span>
            <span class="ml-2">加载中...</span>
          </div>
          <div id="topics-empty" class="text-center py-8 hidden">
            <p class="text-base-content opacity-60">暂无关联选题</p>
          </div>
          <div id="topics-error" class="text-center py-8 hidden">
            <i class="fas fa-exclamation-triangle text-error mr-2"></i>
            <span id="topics-error-message">加载失败，请稍后再试</span>
          </div>
        </div>
        
        <!-- 底部操作栏 -->
        <div class="mt-8 flex justify-end">
          <button class="btn btn-neutral" id="btn-generate-topics" onclick="openMoreTopicsModal(currentMaterialId)">
            <i class="fas fa-magic mr-2"></i>再次生成
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 分页控制 -->
  <div class="flex justify-center mb-6">
    <div class="join">
      <button class="join-item btn btn-sm">«</button>
      <button class="join-item btn btn-sm">‹</button>
      <button class="join-item btn btn-sm">1</button>
      <button class="join-item btn btn-sm btn-active">2</button>
      <button class="join-item btn btn-sm">3</button>
      <button class="join-item btn btn-sm">4</button>
      <button class="join-item btn btn-sm">›</button>
      <button class="join-item btn btn-sm">»</button>
    </div>
  </div>
  
  <!-- 素材分析结果Modal -->
  <dialog id="analysis-modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box w-full max-w-4xl max-h-[90vh]">
      <div class="flex justify-between items-center mb-4 sticky top-0 bg-base-100 z-10 pb-2 border-b border-base-300">
        <h3 class="text-lg font-bold">素材分析结果</h3>
        <form method="dialog">
          <button class="btn btn-sm btn-ghost">
            <i class="fas fa-times"></i>
          </button>
        </form>
      </div>
      
      <div class="overflow-y-auto max-h-[calc(90vh-8rem)]">
        <div class="prose prose-sm max-w-none" id="analysis-content">
          <!-- Markdown内容将通过JavaScript动态加载 -->
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">视频内容概述</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
          
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">脚本结构分析</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
          
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">爆款元素识别</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
          
          <div class="bg-base-200 rounded-lg p-4">
            <h4 class="font-medium mb-2">变现潜力评估</h4>
            <p class="text-sm opacity-70">正在加载分析结果...</p>
          </div>
        </div>
      </div>
      
      <div class="modal-action sticky bottom-0 bg-base-100 pt-4 border-t border-base-300 mt-4">
        <form method="dialog">
          <button class="btn btn-block sm:btn-auto">关闭</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
</body>
</html> 