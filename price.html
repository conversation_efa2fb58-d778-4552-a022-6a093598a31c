<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>付费方案 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    .pricing-card {
      height: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .pricing-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .price-tag {
      font-size: 2.5rem;
      font-weight: 700;
    }
    .price-original {
      text-decoration: line-through;
      opacity: 0.6;
    }
    .feature-check, .feature-cross {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
    }
    .feature-check i {
      color: #10b981;
      margin-right: 0.75rem;
    }
    .feature-cross i {
      color: #ef4444;
      margin-right: 0.75rem;
    }
    .soon-badge {
      margin-left: 0.5rem;
      font-size: 0.7rem;
      background-color: #6366f1;
      color: white;
      padding: 0.15rem 0.5rem;
      border-radius: 1rem;
    }
    .price-card {
      transition: transform 0.3s, box-shadow 0.3s;
    }
    .price-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    .price-feature {
      padding: 0.5rem 0;
      display: flex;
      align-items: center;
    }
    .price-feature i {
      margin-right: 0.5rem;
      font-size: 0.875rem;
    }
    @media (max-width: 767px) {
      .price-cards {
        flex-direction: column;
      }
      .price-card {
        width: 100%;
        margin-bottom: 1.5rem;
      }
    }
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .toast-top {
      top: 2rem;
    }
    .toast-end {
      right: 2rem;
    }
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">付费方案</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <!-- 头部问候 -->
        <header class="mb-6">
          <h1 class="text-2xl font-bold">付费方案</h1>
          <p class="text-base-content opacity-60">小小投入，一百倍回报，一步到位解锁创作捷径</p>
        </header>

        <!-- 价格卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <!-- 免费版 -->
          <div class="card bg-base-100 shadow-md pricing-card">
            <div class="card-body">
              <h2 class="card-title text-xl mb-2">免费版</h2>
              <p class="opacity-70 mb-6">适合初次体验的用户</p>
              <span class="price-tag mb-6">¥0</span>
              
              <div class="divider"></div>
              
              <div class="mb-6">
                <div class="feature-check">
                  <i class="fas fa-check-circle"></i>
                  <span>公开智能体 有限使用</span>
                </div>
                <div class="feature-cross">
                  <i class="fas fa-times-circle"></i>
                  <span>私有智能体定制</span>
                </div>
                <div class="feature-cross">
                  <i class="fas fa-times-circle"></i>
                  <span>自动采集抖音收藏夹</span>
                </div>
                <div class="feature-cross">
                  <i class="fas fa-times-circle"></i>
                  <span>自动分析参考素材</span>
                </div>
                <div class="feature-cross">
                  <i class="fas fa-times-circle"></i>
                  <span>自动生成爆款选题</span>
                </div>
                <div class="feature-cross">
                  <i class="fas fa-times-circle"></i>
                  <span>多模态纯视频理解和拆片</span>
                </div>
              </div>
              
              <div class="card-actions mt-auto">
                <button class="btn btn-outline w-full">当前方案</button>
              </div>
            </div>
          </div>
          
          <!-- 付费版 -->
          <div class="card bg-base-100 shadow-md border-2 border-black pricing-card">
            <div class="card-body">
              <div class="absolute -top-3 -right-3 badge badge-lg bg-black text-white px-3 py-3">推荐</div>
              <h2 class="card-title text-xl mb-2">付费版</h2>
              <p class="opacity-70 mb-6">适合认真创作的专业用户</p>
              <div class="mb-6">
                <span class="price-tag">¥780</span>
                <span class="text-xs opacity-70">/年</span>
                <p class="price-original mt-1">原价 ¥6980/年</p>
                <div class="badge badge-warning mt-2">早鸟价</div>
                <p class="text-sm mt-2 opacity-70">每月1日和15日涨价</p>
              </div>
              
              <div class="divider"></div>
              
              <div class="mb-6">
                <div class="feature-check">
                  <i class="fas fa-check-circle"></i>
                  <span>公开智能体 无限量</span>
                </div>
                <div class="feature-check">
                  <i class="fas fa-check-circle"></i>
                  <span>私有智能体定制 1个</span>
                </div>
                <div class="feature-check">
                  <i class="fas fa-check-circle"></i>
                  <span>自动采集抖音收藏夹 无限量</span>
                </div>
                <div class="feature-check">
                  <i class="fas fa-check-circle"></i>
                  <span>自动分析参考素材 无限量</span>
                </div>
                <div class="feature-check">
                  <i class="fas fa-check-circle"></i>
                  <span>自动生成爆款选题 无限量</span>
                </div>
                <div class="feature-check">
                  <i class="fas fa-check-circle"></i>
                  <span>多模态纯视频理解和拆片</span>
                  <span class="soon-badge">即将上线</span>
                </div>
              </div>
              
              <div class="card-actions mt-auto">
                <button class="btn bg-black text-white hover:bg-gray-800 w-full" onclick="payment_modal.showModal()">
                  <i class="fab fa-weixin mr-1"></i>
                  <i class="fab fa-alipay mr-2"></i>
                  立即升级
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 退款政策 -->
        <div class="card bg-base-100 shadow-md mb-8">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">支持7天无理由退款和开票</h2>
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div class="feature-check">
                <i class="fas fa-check-circle"></i>
                <span>没学会退款</span>
              </div>
              <div class="feature-check">
                <i class="fas fa-check-circle"></i>
                <span>效果不满意退款</span>
              </div>
              <div class="feature-check">
                <i class="fas fa-check-circle"></i>
                <span>心情不好退款</span>
              </div>
              <div class="feature-check">
                <i class="fas fa-check-circle"></i>
                <span>支持开发票</span>
              </div>
              <div class="feature-check">
                <i class="fas fa-check-circle"></i>
                <span>支持对公转账</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 底部信息已删除 -->
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <!-- 菜单项 -->
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-sync mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">5</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <!-- 用户信息 -->
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>

  <!-- 添加支付模态框 -->
  <dialog id="payment_modal" class="modal">
    <div class="modal-box p-0 max-w-md overflow-hidden">
      <!-- 头部 -->
      <div class="bg-black text-white p-4 text-center">
        <h3 class="text-lg font-bold">升级付费版</h3>
      </div>
      
      <div class="p-6">
        <!-- 金额和二维码 -->
        <div class="flex flex-col items-center">
          <div class="flex flex-col items-center mb-6">
            <p class="text-sm opacity-70 mb-1">扫一扫付款（元）</p>
            <p class="text-4xl font-bold">780.00</p>
          </div>
          
          <!-- 二维码区域 -->
          <div class="bg-white p-4 border border-gray-200 rounded-lg shadow-sm mb-6 cursor-pointer relative" onclick="showPaymentSuccess()">
            <img src="data:image/png;base64,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" alt="支付二维码" class="w-52 h-52">
            <div class="absolute bottom-2 right-2 text-xs border border-gray-200 rounded px-1 py-0.5 bg-white opacity-80">
              点击模拟支付
            </div>
          </div>
        </div>
        
        <!-- 订单信息 -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
          <div class="flex justify-between mb-3">
            <span class="text-sm font-medium">订单号</span>
            <span class="text-sm">**********</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm font-medium">商品名称</span>
            <span class="text-sm">起号助手付费版年度订阅</span>
          </div>
        </div>
        
        <!-- 支付方式 -->
        <div class="flex flex-col items-center mb-5">
          <p class="text-sm font-medium mb-3">支持以下应用扫码支付</p>
          <div class="flex justify-center gap-6">
            <div class="flex flex-col items-center">
              <div class="bg-green-500 w-12 h-12 rounded-full flex items-center justify-center">
                <i class="fab fa-weixin text-white text-2xl"></i>
              </div>
              <span class="text-xs mt-1">微信</span>
            </div>
            <div class="flex flex-col items-center">
              <div class="bg-blue-500 w-12 h-12 rounded-full flex items-center justify-center">
                <i class="fab fa-alipay text-white text-2xl"></i>
              </div>
              <span class="text-xs mt-1">支付宝</span>
            </div>
          </div>
        </div>
        
        <!-- 倒计时 -->
        <div class="border-t border-gray-200 pt-4 text-center">
          <div class="inline-flex items-center px-2.5 py-1 rounded-full bg-gray-100">
            <i class="far fa-clock mr-1.5 text-gray-500"></i>
            <span class="text-sm font-medium">二维码剩余 <span id="countdown" class="text-red-500 font-bold">5:00</span> 过期</span>
          </div>
        </div>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>close</button>
    </form>
  </dialog>

  <!-- 支付成功模态框 -->
  <dialog id="payment_success_modal" class="modal">
    <div class="modal-box p-0 max-w-md overflow-hidden">
      <!-- 头部 -->
      <div class="p-6 flex flex-col items-center">
        <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6 animate-pulse">
          <i class="fas fa-check text-green-500 text-5xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-2">支付成功</h3>
        <p class="text-3xl font-bold mb-6">¥780.00</p>
        
        <!-- 订单信息 -->
        <div class="bg-gray-50 rounded-lg p-4 w-full">
          <div class="flex justify-between">
            <span class="text-sm font-medium">订单号</span>
            <span class="text-sm">**********</span>
          </div>
        </div>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>close</button>
    </form>
  </dialog>
  
  <!-- 倒计时脚本 -->
  <script>
    // 倒计时功能
    function startCountdown() {
      let minutes = 5;
      let seconds = 0;
      
      const countdownInterval = setInterval(function() {
        if (seconds === 0) {
          if (minutes === 0) {
            clearInterval(countdownInterval);
            return;
          }
          minutes--;
          seconds = 59;
        } else {
          seconds--;
        }
        
        document.getElementById('countdown').textContent = minutes + ':' + (seconds < 10 ? '0' + seconds : seconds);
      }, 1000);
    }
    
    // 模拟支付成功
    function showPaymentSuccess() {
      payment_modal.close();
      setTimeout(() => {
        payment_success_modal.showModal();
      }, 500);
    }
    
    // 当支付模态框打开时开始倒计时
    document.getElementById('payment_modal').addEventListener('showModal', function() {
      startCountdown();
    });
  </script>

  <!-- 成功提示Toast -->
  <div id="toast-message" class="toast toast-top toast-end opacity-0 transition-opacity duration-300">
    <div role="alert" class="alert alert-success">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>操作成功</span>
    </div>
  </div>

  <script>
    // 显示Toast提示
    function showToast(message, type = 'success') {
      // 创建toast元素
      let toast = document.getElementById('toast-message');
      if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast-message';
        toast.className = 'toast toast-top toast-end opacity-0 transition-opacity duration-300';
        
        let alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-error';
        else if (type === 'warning') alertClass = 'alert-warning';
        else if (type === 'info') alertClass = 'alert-info';
        
        toast.innerHTML = `
          <div role="alert" class="alert ${alertClass}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>${message}</span>
          </div>
        `;
        document.body.appendChild(toast);
      } else {
        toast.querySelector('span').textContent = message;
        
        // 更新alert类型
        let alertDiv = toast.querySelector('.alert');
        alertDiv.classList.remove('alert-success', 'alert-error', 'alert-warning', 'alert-info');
        
        let alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-error';
        else if (type === 'warning') alertClass = 'alert-warning';
        else if (type === 'info') alertClass = 'alert-info';
        
        alertDiv.classList.add(alertClass);
      }
      
      // 显示Toast
      toast.classList.remove('opacity-0');
      toast.classList.add('opacity-100');
      
      // 2秒后自动隐藏
      setTimeout(() => {
        toast.classList.remove('opacity-100');
        toast.classList.add('opacity-0');
      }, 2000);
    }
    
    // 购买会员函数
    function buyMembership(type) {
      showToast(`已选择${type}会员，请完成支付`, 'info');
    }
    
    // 更新侧边栏数量
    function updateSidebarCounts() {
      // 计算素材总数
      const materialCount = 8; // 示例数据
      
      // 计算已确认选题总数
      const topicCount = 5; // 示例数据
      
      // 更新侧边栏徽章
      const materialsBadge = document.querySelector('a[href="assets.html"] .badge');
      const topicsBadge = document.querySelector('a[href="topics.html"] .badge');
      
      if (materialsBadge) {
        materialsBadge.textContent = materialCount;
      }
      
      if (topicsBadge) {
        topicsBadge.textContent = topicCount;
      }
    }
    
    // 页面加载时更新侧边栏数量
    document.addEventListener('DOMContentLoaded', function() {
      updateSidebarCounts();
      
      // 为购买按钮添加事件
      const buyButtons = document.querySelectorAll('.btn-buy');
      buyButtons.forEach(button => {
        button.addEventListener('click', function() {
          const membershipType = this.getAttribute('data-membership-type');
          if (membershipType) {
            buyMembership(membershipType);
          }
        });
      });
    });
  </script>
</body>
</html> 