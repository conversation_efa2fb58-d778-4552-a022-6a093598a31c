<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>行业关键词监控 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
    
    /* Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
    
    /* 关键词卡片样式 */
    .keyword-card {
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    
    .keyword-card:hover {
      shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      border-color: #d1d5db;
    }

    /* 视频缩略图样式 */
    .video-thumbnail {
      width: 80px;
      height: 60px;
      border-radius: 0.25rem;
      object-fit: cover;
    }
    
    /* 文本行限制 */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">行业关键词监控</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <header class="mb-6 hidden lg:block">
          <h1 class="text-2xl font-bold">行业关键词监控</h1>
          <p class="text-base-content opacity-60">监控行业热门关键词，发现相关视频内容</p>
        </header>
        
        <!-- 操作区域 -->
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="add_keyword_modal.showModal()">
            <i class="fas fa-plus mr-2"></i>添加关键词
          </button>
          <div class="flex items-center gap-3">
            <span class="text-sm opacity-60">最后更新：2024-01-20 15:45</span>
            <button class="btn btn-outline btn-sm" onclick="batch_sync_modal.showModal()">
              <i class="fas fa-sync mr-2"></i>批量同步
            </button>
          </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 hidden lg:grid">
          <div class="stat bg-base-100 shadow rounded-box">
            <div class="stat-figure text-primary">
              <i class="fas fa-search text-3xl"></i>
            </div>
            <div class="stat-title">监控关键词</div>
            <div class="stat-value text-primary">8</div>
            <div class="stat-desc">正在监控</div>
          </div>
          
          <div class="stat bg-base-100 shadow rounded-box">
            <div class="stat-figure text-secondary">
              <i class="fas fa-video text-3xl"></i>
            </div>
            <div class="stat-title">发现视频</div>
            <div class="stat-value text-secondary">156</div>
            <div class="stat-desc">总计</div>
          </div>
          
          <div class="stat bg-base-100 shadow rounded-box">
            <div class="stat-figure text-info">
              <i class="fas fa-clock text-3xl"></i>
            </div>
            <div class="stat-title">今日新增</div>
            <div class="stat-value text-info">12</div>
            <div class="stat-desc">新视频</div>
          </div>
        </div>
        
        <!-- 关键词监控列表 -->
        <div class="card bg-base-100 shadow-md">
          <div class="card-body">
            <h3 class="card-title mb-4">关键词监控列表</h3>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <!-- 关键词1 -->
              <div class="keyword-card bg-base-100 p-4">
                <div class="flex items-start gap-3 mb-3">
                  <div class="flex-1">
                    <h4 class="font-medium text-base">家居装修</h4>
                  </div>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-xs">
                      <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                      <li><a onclick="deleteKeyword('1')"><i class="fas fa-trash mr-2"></i>删除</a></li>
                    </ul>
                  </div>
                </div>
                
                <div class="grid grid-cols-2 gap-2 text-center mb-3">
                  <div>
                    <div class="text-lg font-semibold">8</div>
                    <div class="text-xs opacity-60">今日新增</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">42</div>
                    <div class="text-xs opacity-60">累计视频</div>
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <button class="btn btn-xs btn-outline flex-1" onclick="viewKeywordVideos('1', '家居装修')">查看今日新增</button>
                </div>
              </div>
              
              <!-- 关键词2 -->
              <div class="keyword-card bg-base-100 p-4">
                <div class="flex items-start gap-3 mb-3">
                  <div class="flex-1">
                    <h4 class="font-medium text-base">美食制作</h4>
                  </div>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-xs">
                      <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                      <li><a onclick="deleteKeyword('2')"><i class="fas fa-trash mr-2"></i>删除</a></li>
                    </ul>
                  </div>
                </div>
                
                <div class="grid grid-cols-2 gap-2 text-center mb-3">
                  <div>
                    <div class="text-lg font-semibold">3</div>
                    <div class="text-xs opacity-60">今日新增</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">28</div>
                    <div class="text-xs opacity-60">累计视频</div>
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <button class="btn btn-xs btn-outline flex-1" onclick="viewKeywordVideos('2', '美食制作')">查看今日新增</button>
                </div>
              </div>
              
              <!-- 关键词3 -->
              <div class="keyword-card bg-base-100 p-4">
                <div class="flex items-start gap-3 mb-3">
                  <div class="flex-1">
                    <h4 class="font-medium text-base">健身运动</h4>
                  </div>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-xs">
                      <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                      <li><a onclick="deleteKeyword('3')"><i class="fas fa-trash mr-2"></i>删除</a></li>
                    </ul>
                  </div>
                </div>
                
                <div class="grid grid-cols-2 gap-2 text-center mb-3">
                  <div>
                    <div class="text-lg font-semibold">1</div>
                    <div class="text-xs opacity-60">今日新增</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">35</div>
                    <div class="text-xs opacity-60">累计视频</div>
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <button class="btn btn-xs btn-outline flex-1" onclick="viewKeywordVideos('3', '健身运动')">查看今日新增</button>
                </div>
              </div>
              
              <!-- 添加更多关键词占位卡片 -->
              <div class="keyword-card bg-base-100 p-4 border-dashed border-2 border-base-300 flex items-center justify-center min-h-[160px]">
                <div class="text-center">
                  <i class="fas fa-plus text-2xl text-base-300 mb-2"></i>
                  <p class="text-sm opacity-60 mb-2">添加更多关键词</p>
                  <button class="btn btn-sm btn-outline" onclick="add_keyword_modal.showModal()">
                    添加关键词
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">28</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm font-medium menu-active"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 添加关键词模态框 -->
  <dialog id="add_keyword_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-h-[80vh] h-auto sm:max-w-2xl sm:max-h-[80vh]">
      <h3 class="text-lg font-bold mb-6">添加监控关键词</h3>
      
      <!-- 第一步：搜索 -->
      <div id="keyword_search_step" class="space-y-6">
        <div class="form-control">
          <label class="label">
            <span class="label-text">关键词</span>
          </label>
          <div class="join w-full">
            <input id="keyword_input" type="text" class="input input-bordered join-item flex-1" placeholder="请输入要监控的关键词" onkeypress="if(event.key==='Enter') searchKeywordVideos()" />
            <button class="btn join-item bg-black text-white hover:bg-gray-800" onclick="searchKeywordVideos()">
              <i class="fas fa-search mr-2"></i>搜索
            </button>
          </div>
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text">平台</span>
          </label>
          <select id="platform_select" class="select select-bordered w-full" disabled>
            <option selected>抖音</option>
          </select>
        </div>
      </div>
      
      <!-- 第二步：搜索结果 -->
      <div id="keyword_results_step" class="space-y-6 hidden h-full flex flex-col">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h4 id="keyword_results_title" class="font-medium text-lg">搜索结果</h4>
          <button class="btn btn-sm btn-outline" onclick="backToKeywordSearch()">
            <i class="fas fa-arrow-left mr-2"></i>重新搜索
          </button>
        </div>
        
        <!-- 搜索结果列表 -->
        <div id="keyword_search_results" class="space-y-3 flex-1 overflow-y-auto border border-base-300 rounded-lg p-4 max-h-80">
          <!-- 搜索结果将动态插入到这里 -->
        </div>
        
        <div class="flex justify-end">
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="confirmAddKeywordMonitoring()">
            <i class="fas fa-plus mr-2"></i>确认添加监控
          </button>
        </div>
      </div>
      
      <div class="modal-action mt-6" id="keyword_modal_action">
        <form method="dialog">
          <button class="btn btn-ghost">取消</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button onclick="resetAddKeywordModal()">关闭</button>
    </form>
  </dialog>

  <!-- 关键词视频列表模态框 -->
  <dialog id="keyword_videos_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-w-3xl">
      <h3 class="text-xl font-bold mb-4 flex items-center gap-2">
        <i class="fas fa-calendar-plus text-primary"></i>
        <span id="keyword_videos_title">今日新增视频</span>
      </h3>
      
      <div class="divider text-sm opacity-70">今日新增视频列表</div>
      
      <div class="overflow-x-auto max-h-96 overflow-y-auto">
        <table class="table table-sm table-zebra">
          <thead class="bg-base-200 sticky top-0">
            <tr>
              <th class="w-16">#</th>
              <th>标题</th>
              <th>作者</th>
              <th>发布时间</th>
            </tr>
          </thead>
          <tbody id="videos_list">
            <!-- 动态生成的视频列表 -->
          </tbody>
        </table>
      </div>
      
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 批量同步确认模态框 -->
  <dialog id="batch_sync_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">批量同步确认</h3>
      <div class="space-y-4">
        <p>确定要同步所有关键词的最新数据吗？</p>
        <div class="bg-base-200 p-3 rounded-lg">
          <div class="text-sm opacity-80">
            <p>• 将检查所有关键词的最新视频</p>
            <p>• 监控到的数据将自动导入至灵感收件箱</p>
            <p>• 更新关键词监控数据</p>
            <p>• 预计需要 1-2 分钟完成</p>
          </div>
        </div>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="startBatchSync()">确认同步</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 删除关键词确认模态框 -->
  <dialog id="delete_keyword_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认删除</h3>
      <div class="space-y-4">
        <p>您确定要删除关键词 <span id="delete_keyword_name" class="font-medium"></span> 吗？</p>
        <div class="bg-base-200 p-3 rounded-lg">
          <div class="text-sm opacity-80">
            <p>• 删除后将停止监控该关键词的更新</p>
            <p>• 已收集的历史数据将被保留</p>
            <p>• 此操作无法撤销</p>
          </div>
        </div>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white" onclick="confirmDeleteKeyword()">确认删除</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- Toast通知 -->
  <div class="toast toast-top toast-end">
    <!-- 通知将通过JS动态添加 -->
  </div>
  
  <script>
    // 当前要删除的关键词信息
    let keywordToDelete = null;
    // 当前搜索的关键词
    let currentKeyword = null;
    
    // 显示Toast提示
    function showToast(message, type = 'info') {
      const toast = document.querySelector('.toast');
      const alert = document.createElement('div');
      alert.className = `alert alert-${type}`;
      alert.innerHTML = `<span>${message}</span>`;
      toast.appendChild(alert);
      
      setTimeout(() => {
        if (toast.contains(alert)) {
          toast.removeChild(alert);
        }
      }, 3000);
    }
    
    // 搜索关键词相关视频
    function searchKeywordVideos() {
      const keyword = document.getElementById('keyword_input').value.trim();
      
      if (!keyword) {
        showToast('请输入关键词', 'warning');
        return;
      }
      
      currentKeyword = keyword;
      
      // 显示加载状态
      showToast('正在搜索相关视频...', 'info');
      
      // 模拟API搜索（实际应该调用真实API）
      setTimeout(() => {
        const mockResults = generateMockKeywordResults(keyword);
        displayKeywordSearchResults(mockResults);
        
        // 更新搜索结果标题
        document.getElementById('keyword_results_title').textContent = `${keyword}相关视频前 10 条`;
        
        // 切换到结果步骤
        document.getElementById('keyword_search_step').classList.add('hidden');
        document.getElementById('keyword_results_step').classList.remove('hidden');
        document.getElementById('keyword_modal_action').classList.add('hidden');
      }, 1000);
    }
    
    // 生成模拟关键词搜索结果
    function generateMockKeywordResults(keyword) {
      const mockVideos = [
        {
          id: 'video_1',
          title: `${keyword}完整教程 - 新手必看指南`,
          author: '专业导师小李',
          publishTime: '2024-01-20 14:30',
          likes: '3.6万',
          comments: '2.1k',
          favorites: '1.8万',
          shares: '856',
          thumbnail: 'https://picsum.photos/120/90?random=1',
          platform: '抖音'
        },
        {
          id: 'video_2',
          title: `${keyword}技巧分享，这样做效果更好`,
          author: '技巧分享达人',
          publishTime: '2024-01-19 16:45',
          likes: '2.3万',
          comments: '1.5k',
          favorites: '1.2万',
          shares: '432',
          thumbnail: 'https://picsum.photos/120/90?random=2',
          platform: '抖音'
        },
        {
          id: 'video_3',
          title: `${keyword}实用方案，省钱又高效`,
          author: '实用小技巧',
          publishTime: '2024-01-18 09:20',
          likes: '4.8万',
          comments: '3.2k',
          favorites: '2.6万',
          shares: '1.1k',
          thumbnail: 'https://picsum.photos/120/90?random=3',
          platform: '抖音'
        },
        {
          id: 'video_4',
          title: `${keyword}注意事项，避免这些误区`,
          author: '专业指导',
          publishTime: '2024-01-17 20:15',
          likes: '1.9万',
          comments: '892',
          favorites: '1.1万',
          shares: '245',
          thumbnail: 'https://picsum.photos/120/90?random=4',
          platform: '抖音'
        },
        {
          id: 'video_5',
          title: `${keyword}对比分析，哪种方法最好`,
          author: '对比测评',
          publishTime: '2024-01-16 11:35',
          likes: '3.1万',
          comments: '1.8k',
          favorites: '1.9万',
          shares: '672',
          thumbnail: 'https://picsum.photos/120/90?random=5',
          platform: '抖音'
        },
        {
          id: 'video_6',
          title: `${keyword}常见问题解答，一次搞懂`,
          author: '问题解答专家',
          publishTime: '2024-01-15 15:50',
          likes: '2.7万',
          comments: '1.3k',
          favorites: '1.5万',
          shares: '398',
          thumbnail: 'https://picsum.photos/120/90?random=6',
          platform: '抖音'
        },
        {
          id: 'video_7',
          title: `${keyword}最新趋势，跟上时代步伐`,
          author: '趋势观察员',
          publishTime: '2024-01-14 13:25',
          likes: '5.2万',
          comments: '2.9k',
          favorites: '3.1万',
          shares: '1.4k',
          thumbnail: 'https://picsum.photos/120/90?random=7',
          platform: '抖音'
        },
        {
          id: 'video_8',
          title: `${keyword}进阶技巧，高手必备`,
          author: '进阶教程',
          publishTime: '2024-01-13 18:40',
          likes: '1.6万',
          comments: '756',
          favorites: '923',
          shares: '178',
          thumbnail: 'https://picsum.photos/120/90?random=8',
          platform: '抖音'
        },
        {
          id: 'video_9',
          title: `${keyword}工具推荐，提升效率必备`,
          author: '工具推荐师',
          publishTime: '2024-01-12 10:15',
          likes: '3.8万',
          comments: '2.4k',
          favorites: '2.2万',
          shares: '889',
          thumbnail: 'https://picsum.photos/120/90?random=9',
          platform: '抖音'
        },
        {
          id: 'video_10',
          title: `${keyword}案例分析，成功经验分享`,
          author: '案例分析专家',
          publishTime: '2024-01-11 19:30',
          likes: '2.2万',
          comments: '1.1k',
          favorites: '1.3万',
          shares: '456',
          thumbnail: 'https://picsum.photos/120/90?random=10',
          platform: '抖音'
        }
      ];
      
      return mockVideos.slice(0, 10);
    }
    
    // 显示关键词搜索结果
    function displayKeywordSearchResults(results) {
      const container = document.getElementById('keyword_search_results');
      
      if (results.length === 0) {
        container.innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-search text-3xl text-base-300 mb-3"></i>
            <p class="text-base-content opacity-60">未找到相关视频</p>
          </div>
        `;
        return;
      }
      
      container.innerHTML = results.map(video => `
        <div class="border border-base-300 rounded-lg p-3 hover:bg-base-50 transition-colors">
          <div class="flex gap-3">
            <img src="${video.thumbnail}" alt="视频缩略图" class="video-thumbnail flex-shrink-0">
            <div class="flex-1 min-w-0">
              <h5 class="font-medium mb-2 line-clamp-2">${video.title}</h5>
              <div class="flex flex-wrap items-center gap-2 text-xs opacity-60 mb-2">
                <span><i class="fas fa-user mr-1"></i>${video.author}</span>
                <span><i class="fas fa-calendar mr-1"></i>${video.publishTime}</span>
                <span><i class="fas fa-heart mr-1"></i>${video.likes}</span>
                <span><i class="fas fa-comment mr-1"></i>${video.comments}</span>
                <span><i class="fas fa-star mr-1"></i>${video.favorites}</span>
                <span><i class="fas fa-share mr-1"></i>${video.shares}</span>
              </div>
            </div>
          </div>
        </div>
      `).join('');
    }
    
    // 返回关键词搜索步骤
    function backToKeywordSearch() {
      document.getElementById('keyword_results_step').classList.add('hidden');
      document.getElementById('keyword_search_step').classList.remove('hidden');
      document.getElementById('keyword_modal_action').classList.remove('hidden');
      currentKeyword = null;
    }
    
    // 确认添加关键词监控
    function confirmAddKeywordMonitoring() {
      if (!currentKeyword) {
        showToast('请先搜索关键词', 'warning');
        return;
      }
      
      // 这里应该调用API添加关键词监控
      showToast(`关键词 "${currentKeyword}" 添加成功，开始监控`, 'success');
      
      // 关闭模态框并重置
      document.getElementById('add_keyword_modal').close();
      resetAddKeywordModal();
    }
    
    // 重置添加关键词模态框
    function resetAddKeywordModal() {
      document.getElementById('keyword_input').value = '';
      document.getElementById('keyword_search_results').innerHTML = '';
      document.getElementById('keyword_results_step').classList.add('hidden');
      document.getElementById('keyword_search_step').classList.remove('hidden');
      document.getElementById('keyword_modal_action').classList.remove('hidden');
      document.getElementById('platform_select').selectedIndex = 0;
      currentKeyword = null;
    }
    
    // 添加关键词（保留原函数名以兼容）
    function addKeyword() {
      searchKeywordVideos();
    }
    
    // 删除关键词
    function deleteKeyword(keywordId) {
      // 获取关键词信息（这里简化处理，实际应该从数据中获取）
      const keywordNames = {
        '1': '家居装修',
        '2': '美食制作',
        '3': '健身运动'
      };
      
      // 设置要删除的关键词信息
      keywordToDelete = {
        id: keywordId,
        name: keywordNames[keywordId] || '未知关键词'
      };
      
      // 更新模态框中的关键词名称
      document.getElementById('delete_keyword_name').textContent = keywordToDelete.name;
      
      // 显示确认模态框
      document.getElementById('delete_keyword_modal').showModal();
    }
    
    // 确认删除关键词
    function confirmDeleteKeyword() {
      if (keywordToDelete) {
        // 关闭确认模态框
        document.getElementById('delete_keyword_modal').close();
        
        // 显示删除成功提示
        showToast(`已删除关键词 ${keywordToDelete.name}`, 'success');
        
        // 这里应该调用API删除关键词，并更新UI
        // 实际项目中应该移除对应的DOM元素或重新渲染列表
        
        // 重置删除关键词信息
        keywordToDelete = null;
      }
    }
    
    // 查看关键词相关视频
    function viewKeywordVideos(keywordId, keywordName) {
      // 获取今日新增数量（这里简化处理，实际应该从数据中获取）
      const todayNewCounts = {
        '1': 8,  // 家居装修今日新增8个
        '2': 3,  // 美食制作今日新增3个
        '3': 1   // 健身运动今日新增1个
      };
      
      const todayNewCount = todayNewCounts[keywordId] || 0;
      
      // 设置Modal标题为"{{关键词}} - 今日新增"
      document.getElementById('keyword_videos_title').textContent = `${keywordName} - 今日新增`;
      
      // 生成今日新增视频数据
      const todayVideos = generateTodayNewVideos(keywordName, todayNewCount);
      displayTodayVideos(todayVideos);
      
      document.getElementById('keyword_videos_modal').showModal();
    }
    
    // 生成今日新增视频数据
    function generateTodayNewVideos(keywordName, count) {
      const baseVideos = [
        {
          title: `新房装修完工，这些细节一定要注意！`,
          author: '装修达人小李',
          publishTime: '2024-01-20 14:30',
          likes: '3.6万',
          comments: '2.1k',
          favorites: '1.8万',
          shares: '856',
          thumbnail: 'https://picsum.photos/120/90?random=11'
        },
        {
          title: `装修预算控制技巧，省钱又实用`,
          author: '家装小能手',
          publishTime: '2024-01-20 16:45',
          likes: '2.3万',
          comments: '1.5k',
          favorites: '1.2万',
          shares: '432',
          thumbnail: 'https://picsum.photos/120/90?random=12'
        },
        {
          title: `小户型装修设计，空间利用最大化`,
          author: '设计师阿强',
          publishTime: '2024-01-20 09:20',
          likes: '4.8万',
          comments: '3.2k',
          favorites: '2.6万',
          shares: '1.1k',
          thumbnail: 'https://picsum.photos/120/90?random=13'
        },
        {
          title: `厨房装修避坑指南，新手必看`,
          author: '厨房设计专家',
          publishTime: '2024-01-20 20:15',
          likes: '1.9万',
          comments: '892',
          favorites: '1.1万',
          shares: '245',
          thumbnail: 'https://picsum.photos/120/90?random=14'
        },
        {
          title: `卫生间防水怎么做？详细步骤分享`,
          author: '防水专家老王',
          publishTime: '2024-01-20 11:35',
          likes: '3.1万',
          comments: '1.8k',
          favorites: '1.9万',
          shares: '672',
          thumbnail: 'https://picsum.photos/120/90?random=15'
        },
        {
          title: `客厅装修风格选择，找到适合你的`,
          author: '风格搭配师',
          publishTime: '2024-01-20 15:50',
          likes: '2.7万',
          comments: '1.3k',
          favorites: '1.5万',
          shares: '398',
          thumbnail: 'https://picsum.photos/120/90?random=16'
        },
        {
          title: `卧室装修注意事项，睡得更舒服`,
          author: '舒适空间设计',
          publishTime: '2024-01-20 13:25',
          likes: '2.2万',
          comments: '1.1k',
          favorites: '1.3万',
          shares: '456',
          thumbnail: 'https://picsum.photos/120/90?random=17'
        },
        {
          title: `阳台改造实用方案，多功能设计`,
          author: '空间改造师',
          publishTime: '2024-01-20 18:40',
          likes: '1.6万',
          comments: '756',
          favorites: '923',
          shares: '178',
          thumbnail: 'https://picsum.photos/120/90?random=18'
        }
      ];
      
      // 根据关键词调整标题
      const adjustedVideos = baseVideos.map((video, index) => ({
        ...video,
        id: `today_video_${index + 1}`,
        title: video.title.replace(/装修|设计/, keywordName)
      }));
      
      return adjustedVideos.slice(0, count);
    }
    
    // 显示今日新增视频
    function displayTodayVideos(videos) {
      const container = document.getElementById('videos_list');
      
      if (videos.length === 0) {
        container.innerHTML = `
          <tr>
            <td colspan="4" class="text-center py-8">
              <i class="fas fa-calendar text-3xl text-base-300 mb-3"></i>
              <p class="text-base-content opacity-60">今日暂无新增视频</p>
            </td>
          </tr>
        `;
        return;
      }
      
      container.innerHTML = videos.map((video, index) => `
        <tr class="hover">
          <td>${index + 1}</td>
          <td class="font-medium max-w-xs">
            <div class="line-clamp-2">${video.title}</div>
          </td>
          <td>${video.author}</td>
          <td>${video.publishTime}</td>
        </tr>
      `).join('');
    }
    
    // 导入视频
    function importVideo(videoId) {
      // 这里应该调用API导入视频到素材库
      showToast('视频已导入到素材库', 'success');
    }
    
    // 开始批量同步
    function startBatchSync() {
      // 关闭确认modal
      document.getElementById('batch_sync_modal').close();
      
      // 显示同步开始提示
      showToast('开始批量同步，请稍候...', 'info');
      
      // 模拟同步过程（实际应该调用真实的API）
      setTimeout(() => {
        // 更新统计数据（这里是模拟的，实际应该从API获取）
        updateSyncTime();
        showToast('批量同步完成！共更新了18个新视频', 'success');
      }, 2000);
    }
    
    // 更新同步时间
    function updateSyncTime() {
      const now = new Date();
      const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
      
      // 更新页面上的时间显示
      const timeElement = document.querySelector('.flex.items-center.gap-3 span');
      if (timeElement) {
        timeElement.textContent = `最后更新：${timeString}`;
      }
    }
  </script>
</body>
</html> 