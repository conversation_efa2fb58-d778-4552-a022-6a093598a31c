<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>对标账号监控 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
    
    /* Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
    
    /* 账号卡片样式 */
    .account-card {
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    
    .account-card:hover {
      shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      border-color: #d1d5db;
    }
    
    .account-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
    }
    
    /* 状态指示器 */
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 0.5rem;
    }
    
    .status-active {
      background-color: #10b981;
    }
    
    .status-inactive {
      background-color: #6b7280;
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">对标账号监控</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <header class="mb-6 hidden lg:block">
          <h1 class="text-2xl font-bold">对标账号监控</h1>
          <p class="text-base-content opacity-60">监控竞品账号动态，获取最新灵感和素材</p>
        </header>
        
        <!-- 操作区域 -->
        <div class="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="add_account_modal.showModal()">
            <i class="fas fa-plus mr-2"></i>添加账号
          </button>
          <div class="flex items-center gap-3">
            <span class="text-sm opacity-60">最后更新：2024-01-20 14:30</span>
            <button class="btn btn-outline btn-sm" onclick="batch_sync_modal.showModal()">
              <i class="fas fa-sync mr-2"></i>批量同步
            </button>
          </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 hidden lg:grid">
          <div class="stat bg-base-100 shadow rounded-box">
            <div class="stat-figure text-primary">
              <i class="fas fa-eye text-3xl"></i>
            </div>
            <div class="stat-title">监控账号</div>
            <div class="stat-value text-primary">5</div>
            <div class="stat-desc">活跃监控中</div>
          </div>
          
          <div class="stat bg-base-100 shadow rounded-box">
            <div class="stat-figure text-secondary">
              <i class="fas fa-video text-3xl"></i>
            </div>
            <div class="stat-title">今日更新</div>
            <div class="stat-value text-secondary">12</div>
            <div class="stat-desc">新视频</div>
          </div>
          
          <div class="stat bg-base-100 shadow rounded-box">
            <div class="stat-figure text-info">
              <i class="fas fa-clock text-3xl"></i>
            </div>
            <div class="stat-title">累计更新</div>
            <div class="stat-value text-info">158</div>
            <div class="stat-desc">总视频数</div>
          </div>
        </div>
        
        <!-- 监控账号列表 -->
        <div class="card bg-base-100 shadow-md">
          <div class="card-body">
            <h3 class="card-title mb-4">监控账号列表</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <!-- 账号1 -->
              <div class="account-card bg-base-100 p-4">
                <div class="flex items-start gap-3 mb-3">
                  <img src="./assets/avatar_chon.jpg" alt="账号头像" class="account-avatar">
                  <div class="flex-1">
                    <h4 class="font-medium text-base">装修达人</h4>
                    <p class="text-sm opacity-60">@zhuangxiu_daren</p>
                    <div class="flex items-center mt-1">
                      <div class="status-dot status-active"></div>
                      <span class="text-xs text-success">监控中</span>
                    </div>
                  </div>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-xs">
                      <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                      <li><a onclick="deleteAccount('1')"><i class="fas fa-trash mr-2"></i>删除</a></li>
                    </ul>
                  </div>
                </div>
                
                <div class="grid grid-cols-3 gap-2 text-center mb-3">
                  <div>
                    <div class="text-lg font-semibold">126万</div>
                    <div class="text-xs opacity-60">粉丝</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">245</div>
                    <div class="text-xs opacity-60">作品</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">5</div>
                    <div class="text-xs opacity-60">今日新增</div>
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <button class="btn btn-xs btn-outline flex-1" onclick="viewAccountDetail('1')">查看详情</button>
                </div>
              </div>
              
              <!-- 账号2 -->
              <div class="account-card bg-base-100 p-4">
                <div class="flex items-start gap-3 mb-3">
                  <img src="./assets/avatar_fangqi_kiki.jpeg" alt="账号头像" class="account-avatar">
                  <div class="flex-1">
                    <h4 class="font-medium text-base">效率专家</h4>
                    <p class="text-sm opacity-60">@xiaolv_zhuanjia</p>
                    <div class="flex items-center mt-1">
                      <div class="status-dot status-active"></div>
                      <span class="text-xs text-success">监控中</span>
                    </div>
                  </div>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-xs">
                      <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                      <li><a onclick="deleteAccount('2')"><i class="fas fa-trash mr-2"></i>删除</a></li>
                    </ul>
                  </div>
                </div>
                
                <div class="grid grid-cols-3 gap-2 text-center mb-3">
                  <div>
                    <div class="text-lg font-semibold">89万</div>
                    <div class="text-xs opacity-60">粉丝</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">312</div>
                    <div class="text-xs opacity-60">作品</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">3</div>
                    <div class="text-xs opacity-60">今日新增</div>
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <button class="btn btn-xs btn-outline flex-1" onclick="viewAccountDetail('2')">查看详情</button>
                </div>
              </div>
              
              <!-- 账号3 -->
              <div class="account-card bg-base-100 p-4">
                <div class="flex items-start gap-3 mb-3">
                  <img src="./assets/avatar_jinqiangdashu.jpeg" alt="账号头像" class="account-avatar">
                  <div class="flex-1">
                    <h4 class="font-medium text-base">摄影小技巧</h4>
                    <p class="text-sm opacity-60">@sheying_tips</p>
                    <div class="flex items-center mt-1">
                      <div class="status-dot status-active"></div>
                      <span class="text-xs text-success">监控中</span>
                    </div>
                  </div>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-xs">
                      <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                      <li><a onclick="deleteAccount('3')"><i class="fas fa-trash mr-2"></i>删除</a></li>
                    </ul>
                  </div>
                </div>
                
                <div class="grid grid-cols-3 gap-2 text-center mb-3">
                  <div>
                    <div class="text-lg font-semibold">156万</div>
                    <div class="text-xs opacity-60">粉丝</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">189</div>
                    <div class="text-xs opacity-60">作品</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold">2</div>
                    <div class="text-xs opacity-60">今日新增</div>
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <button class="btn btn-xs btn-outline flex-1" onclick="viewAccountDetail('3')">查看详情</button>
                </div>
              </div>
              
              <!-- 添加更多账号占位卡片 -->
              <div class="account-card bg-base-100 p-4 border-dashed border-2 border-base-300 flex items-center justify-center min-h-[200px]">
                <div class="text-center">
                  <i class="fas fa-plus text-3xl text-base-300 mb-3"></i>
                  <p class="text-sm opacity-60 mb-3">添加更多监控账号</p>
                  <button class="btn btn-sm btn-outline" onclick="add_account_modal.showModal()">
                    添加账号
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">28</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm font-medium menu-active"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 添加监控账号模态框 -->
  <dialog id="add_account_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-h-[80vh] h-auto sm:max-w-2xl sm:max-h-[80vh]">
      <h3 class="text-lg font-bold mb-6">添加监控账号</h3>
      
      <!-- 第一步：搜索 -->
      <div id="search_step" class="space-y-6">
        <div class="form-control">
          <div class="join w-full">
            <input id="search_input" type="text" class="input input-bordered join-item flex-1" placeholder="请输入抖音账号或昵称" onkeypress="if(event.key==='Enter') searchAccounts()" />
            <button class="btn join-item bg-black text-white hover:bg-gray-800" onclick="searchAccounts()">
              <i class="fas fa-search mr-2"></i>搜索
            </button>
          </div>
        </div>
      </div>
      
      <!-- 第二步：搜索结果 -->
      <div id="results_step" class="space-y-6 hidden h-full flex flex-col">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h4 class="font-medium text-lg">搜索结果</h4>
          <button class="btn btn-sm btn-outline" onclick="backToSearch()">
            <i class="fas fa-arrow-left mr-2"></i>重新搜索
          </button>
        </div>
        
        <!-- 搜索结果列表 -->
        <div id="search_results" class="space-y-4 flex-1 overflow-y-auto border border-base-300 rounded-lg p-4">
          <!-- 搜索结果将动态插入到这里 -->
        </div>
      </div>
      
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">取消</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>close</button>
    </form>
  </dialog>
  
  <!-- 账号详情模态框 -->
  <dialog id="account_detail_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">账号详情</h3>
      <div class="space-y-4">
        <div class="flex items-center gap-4">
          <img src="./assets/avatar_chon.jpg" alt="账号头像" class="w-16 h-16 rounded-full">
          <div>
            <div class="flex items-center gap-2 mb-2">
              <h4 class="text-lg font-medium">装修达人</h4>
              <span class="text-sm opacity-60">@zhuangxiu_daren</span>
            </div>
            <div class="flex items-center gap-4 text-sm">
              <span>126万粉丝</span>
              <span>245作品</span>
              <span>8562万获赞</span>
            </div>
          </div>
        </div>
        
        <div class="divider"></div>
        
        <div>
          <h4 class="font-medium mb-2">最新视频</h4>
          <div class="overflow-x-auto max-h-80 overflow-y-auto">
            <table class="table table-sm">
              <thead>
                <tr class="bg-base-200">
                  <th class="font-medium text-base-content w-3/5">标题</th>
                  <th class="font-medium text-base-content w-2/5 text-center">发布时间</th>
                </tr>
              </thead>
              <tbody>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">新房装修完，怎么除甲醛？</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-20 14:30</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">装修预算如何控制？</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-20 09:15</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">小户型装修技巧分享</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-19 20:45</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">厨房装修避坑指南</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-19 16:20</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">卫生间防水怎么做？</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-19 11:30</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">客厅装修风格选择</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-18 19:15</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">卧室装修注意事项</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-18 14:40</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">阳台改造实用方案</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-18 10:25</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">水电改造全攻略</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-17 21:10</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">瓷砖选择与铺贴技巧</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-17 17:35</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">油漆涂料选购指南</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-17 13:20</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">家具选购省钱攻略</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-16 20:50</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">窗帘搭配技巧分享</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-16 15:15</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">灯具选择与布局</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-16 11:40</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">收纳空间设计方案</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-15 19:25</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">装修材料环保选择</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-15 16:10</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">装修工期安排技巧</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-15 12:30</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">装修监理要点解析</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-14 18:45</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">装修合同注意事项</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-14 14:20</td>
                </tr>
                <tr class="hover:bg-base-50">
                  <td class="font-medium text-base-content">装修验收标准详解</td>
                  <td class="text-sm text-base-content opacity-70 text-center">2024-01-14 10:55</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>close</button>
    </form>
  </dialog>
  
  <!-- 批量同步确认模态框 -->
  <dialog id="batch_sync_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">批量同步确认</h3>
      <div class="space-y-4">
        <p>确定要同步所有监控账号的最新数据吗？</p>
        <div class="bg-base-200 p-3 rounded-lg">
          <div class="text-sm opacity-80">
            <p>• 将检查所有账号的最新视频</p>
            <p>• 监控到的数据将自动导入至灵感收件箱</p>
            <p>• 预计需要 1-2 分钟完成</p>
          </div>
        </div>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="startBatchSync()">确认同步</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 删除账号确认模态框 -->
  <dialog id="delete_account_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认删除</h3>
      <div class="space-y-4">
        <p>您确定要删除监控账号 <span id="delete_account_name" class="font-medium"></span> 吗？</p>
        <div class="bg-base-200 p-3 rounded-lg">
          <div class="text-sm opacity-80">
            <p>• 删除后将停止监控该账号的更新</p>
            <p>• 已收集的历史数据将被保留</p>
            <p>• 此操作无法撤销</p>
          </div>
        </div>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white" onclick="confirmDeleteAccount()">确认删除</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <script>
    // 当前选中的账号
    let selectedAccount = null;
    
    // 当前要删除的账号信息
    let accountToDelete = null;
    
    // 搜索账号
    function searchAccounts() {
      const searchInput = document.getElementById('search_input');
      const query = searchInput.value.trim();
      
      if (!query) {
        showToast('请输入搜索内容', 'warning');
        return;
      }
      
      // 显示加载状态
      showToast('正在搜索...', 'info');
      
      // 模拟API搜索（实际应该调用真实API）
      setTimeout(() => {
        const mockResults = generateMockSearchResults(query);
        displaySearchResults(mockResults);
        
        // 切换到结果步骤
        document.getElementById('search_step').classList.add('hidden');
        document.getElementById('results_step').classList.remove('hidden');
      }, 1000);
    }
    
    // 生成模拟搜索结果
    function generateMockSearchResults(query) {
      const mockAccounts = [
        {
          id: 'acc_1',
          avatar: './assets/avatar_chon.jpg',
          nickname: '装修小达人',
          username: '@zhuangxiu_xiaodaren',
          followers: '126万',
          videos: 245,
          likes: '8562万',
          verified: true,
          description: '专注家装干货分享，让装修不踩坑'
        },
        {
          id: 'acc_2',
          avatar: './assets/avatar_fangqi_kiki.jpeg',
          nickname: '效率专家小李',
          username: '@xiaolv_xiaoli',
          followers: '89万',
          videos: 312,
          likes: '5234万',
          verified: false,
          description: '分享工作效率提升技巧，让生活更简单'
        },
        {
          id: 'acc_3',
          avatar: './assets/avatar_jinqiangdashu.jpeg',
          nickname: '摄影技巧分享',
          username: '@sheying_tips_share',
          followers: '156万',
          videos: 189,
          likes: '9876万',
          verified: true,
          description: '手机摄影技巧，人人都能拍大片'
        },
        {
          id: 'acc_4',
          avatar: './assets/avatar_chon.jpg',
          nickname: '美食制作教程',
          username: '@meishi_jiaocheng',
          followers: '203万',
          videos: 456,
          likes: '1.2亿',
          verified: true,
          description: '简单易学的美食制作，新手也能做大厨'
        },
        {
          id: 'acc_5',
          avatar: './assets/avatar_fangqi_kiki.jpeg',
          nickname: '旅行攻略达人',
          username: '@lvxing_gonglue',
          followers: '95万',
          videos: 123,
          likes: '3456万',
          verified: false,
          description: '精选旅行攻略，带你看遍世界美景'
        }
      ];
      
      // 根据查询条件过滤结果（这里简化处理，实际应该有更复杂的匹配逻辑）
      return mockAccounts.slice(0, Math.min(5, Math.floor(Math.random() * 8) + 3));
    }
    
    // 显示搜索结果
    function displaySearchResults(results) {
      const container = document.getElementById('search_results');
      
      if (results.length === 0) {
        container.innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-search text-3xl text-base-300 mb-3"></i>
            <p class="text-base-content opacity-60">未找到相关账号</p>
          </div>
        `;
        return;
      }
      
      container.innerHTML = results.map(account => `
        <div class="border border-base-300 rounded-lg p-4 hover:bg-base-50 transition-colors">
          <div class="flex items-start gap-4">
            <div class="avatar">
              <div class="w-12 h-12 rounded-full">
                <img src="${account.avatar}" alt="头像" class="object-cover">
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-3">
                <h5 class="font-medium text-base truncate">${account.nickname}</h5>
                <span class="text-sm opacity-60">${account.username}</span>
              </div>
              <div class="flex items-center gap-4 text-sm opacity-60">
                <span><i class="fas fa-users mr-1"></i>${account.followers}</span>
                <span><i class="fas fa-video mr-1"></i>${account.videos}作品</span>
                <span><i class="fas fa-heart mr-1"></i>${account.likes || '0'}获赞</span>
              </div>
            </div>
            <div class="flex-shrink-0">
              <button class="btn btn-sm bg-black text-white hover:bg-gray-800" 
                      onclick="selectAccount('${account.id}', this)" 
                      data-account='${JSON.stringify(account)}'>
                添加监控
              </button>
            </div>
          </div>
        </div>
      `).join('');
    }
    
    // 选择账号
    function selectAccount(accountId, buttonElement) {
      selectedAccount = JSON.parse(buttonElement.getAttribute('data-account'));
      
      // 这里应该调用API添加监控账号
      showToast(`已添加 ${selectedAccount.nickname} 到监控列表`, 'success');
      
      // 关闭modal并重置
      document.getElementById('add_account_modal').close();
      resetAddAccountModal();
    }
    
    // 返回搜索步骤
    function backToSearch() {
      document.getElementById('results_step').classList.add('hidden');
      document.getElementById('search_step').classList.remove('hidden');
      selectedAccount = null;
    }
    
    // 重置添加账号模态框
    function resetAddAccountModal() {
      document.getElementById('search_input').value = '';
      document.getElementById('search_results').innerHTML = '';
      document.getElementById('results_step').classList.add('hidden');
      document.getElementById('search_step').classList.remove('hidden');
      selectedAccount = null;
    }
    
    // 查看账号详情
    function viewAccountDetail(accountId) {
      // 这里应该根据accountId加载对应的账号详情
      document.getElementById('account_detail_modal').showModal();
    }
    
    // 删除账号
    function deleteAccount(accountId) {
      // 获取账号信息（这里简化处理，实际应该从数据中获取）
      const accountNames = {
        '1': '装修达人',
        '2': '效率专家', 
        '3': '摄影小技巧'
      };
      
      // 设置要删除的账号信息
      accountToDelete = {
        id: accountId,
        name: accountNames[accountId] || '未知账号'
      };
      
      // 更新模态框中的账号名称
      document.getElementById('delete_account_name').textContent = accountToDelete.name;
      
      // 显示确认模态框
      document.getElementById('delete_account_modal').showModal();
    }
    
    // 确认删除账号
    function confirmDeleteAccount() {
      if (accountToDelete) {
        // 关闭确认模态框
        document.getElementById('delete_account_modal').close();
        
        // 显示删除成功提示
        showToast(`已删除监控账号 ${accountToDelete.name}`, 'success');
        
        // 这里应该调用API删除账号，并更新UI
        // 实际项目中应该移除对应的DOM元素或重新渲染列表
        
        // 重置删除账号信息
        accountToDelete = null;
      }
    }
    
    // 开始批量同步
    function startBatchSync() {
      // 关闭确认modal
      document.getElementById('batch_sync_modal').close();
      
      // 显示同步开始提示
      showToast('开始批量同步，请稍候...', 'info');
      
      // 模拟同步过程（实际应该调用真实的API）
      setTimeout(() => {
        // 更新统计数据（这里是模拟的，实际应该从API获取）
        updateSyncTime();
        showToast('批量同步完成！共更新了15个新视频', 'success');
      }, 2000);
    }
    
    // 更新同步时间
    function updateSyncTime() {
      const now = new Date();
      const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
      
      // 更新页面上的时间显示
      const timeElement = document.querySelector('.flex.items-center.gap-3 span');
      if (timeElement) {
        timeElement.textContent = `最后更新：${timeString}`;
      }
    }
    
    // 显示Toast提示
    function showToast(message, type = 'success') {
      const toastConfigs = {
        success: {
          alertClass: 'alert-success',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>`
        },
        info: {
          alertClass: 'alert-info',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="h-6 w-6 shrink-0 stroke-current">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
          </svg>`
        },
        warning: {
          alertClass: 'alert-warning',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>`
        },
        error: {
          alertClass: 'alert-error',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>`
        }
      };
      
      const config = toastConfigs[type] || toastConfigs.success;
      
      const toastContainer = document.createElement('div');
      toastContainer.className = 'toast toast-top toast-end';
      toastContainer.innerHTML = `
        <div role="alert" class="alert ${config.alertClass}">
          ${config.icon}
          <span>${message}</span>
        </div>
      `;
      
      document.body.appendChild(toastContainer);
      
      setTimeout(() => {
        toastContainer.style.opacity = '1';
      }, 10);
      
      setTimeout(() => {
        toastContainer.style.opacity = '0';
        setTimeout(() => {
          if (toastContainer.parentNode) {
            document.body.removeChild(toastContainer);
          }
        }, 300);
      }, 3000);
    }
  </script>
</body>
</html> 