import React, { useState, useRef, useEffect } from 'react';
import { Button, Card, Modal } from 'react-daisyui';
import { toast } from 'react-toastify';
import {
  trendInsightApi,
} from '../api/generated/client';
import type { components } from '../api/generated/types';
import { formatDateTime } from '@/utils';

type UserKeywordInfo = components['schemas']['UserKeywordInfo'];

type TrendInsightVideoResponseSchema = components['schemas']['VideoItem'];

// 格式化数字显示（万位格式）
const formatNumber = (num: number | string | undefined): string => {
  if (!num || num === 0) return '0';
  const number = typeof num === 'string' ? parseInt(num) || 0 : num;

  if (number >= 10000) {
    // 大于等于1万，使用万为单位
    return (number / 10000).toFixed(1).replace(/\.0$/, '') + '万';
  } else if (number >= 1000) {
    // 大于等于1千，使用k为单位
    return (number / 1000).toFixed(1).replace(/\.0$/, '') + 'k';
  } else {
    // 小于1千，直接显示数字
    return number.toString();
  }
};

// 格式化时间显示（处理时间戳或日期字符串）
const formatTime = (timeStr: string | number | undefined): string => {
  if (!timeStr) return '';

  try {
    let date: Date;

    // 如果是数字（时间戳），需要判断是秒还是毫秒
    if (typeof timeStr === 'number' || /^\d+$/.test(timeStr.toString())) {
      const timestamp = typeof timeStr === 'string' ? parseInt(timeStr) : timeStr;
      // 如果时间戳长度为10位，说明是秒级时间戳，需要转换为毫秒
      date = new Date(timestamp.toString().length === 10 ? timestamp * 1000 : timestamp);
    } else {
      // 如果是字符串格式的日期
      date = new Date(timeStr);
    }

    // 检查日期是否有效
    if (Number.isNaN(date.getTime())) {
      return timeStr.toString();
    }

    // 格式化为 YYYY-MM-DD HH:mm:ss
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch {
    return timeStr.toString();
  }
};



const KeywordMonitoring: React.FC = () => {
  const [keywords, setKeywords] = useState<UserKeywordInfo[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchResults, setSearchResults] = useState<TrendInsightVideoResponseSchema[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [keywordToDelete, setKeywordToDelete] = useState<UserKeywordInfo | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isAddingKeyword, setIsAddingKeyword] = useState(false);
  const [videoStats, setVideoStats] = useState({
    total: 0,
    active: 0,
    todayTotal: 0,
    totalVideos: 0
  });

  const addKeywordModalRef = useRef<HTMLDialogElement>(null);
  const keywordVideosModalRef = useRef<HTMLDialogElement>(null);
  const batchSyncModalRef = useRef<HTMLDialogElement>(null);
  const deleteConfirmModalRef = useRef<HTMLDialogElement>(null);



  // 获取关键词列表
  const fetchKeywords = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await trendInsightApi.getUserVideoKeywords({
        page: 1,
        page_size: 50
      });

      if (response.code === 0) {
        if (response.data?.list) {
          setKeywords(response.data.list);
          setError(null); // 清除之前的错误
        } else {
          // API成功但没有数据
          setKeywords([]);
          setError(null);
        }
      } else {
        // API返回错误状态
        const errorMessage = response.msg || `API返回错误状态码: ${response.code}`;
        console.error('API返回异常:', response);
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error('获取关键词列表失败:', error);
      const errorMessage = error instanceof Error ? `获取数据失败: ${error.message}` : '获取数据失败';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取视频关键词统计信息
  const fetchVideoStats = async () => {
    try {
      const response = await trendInsightApi.getVideoKeywordStats();
      
      if (response.code === 0 && response.data) {
        setVideoStats({
          total: response.data.keyword_count || 0,
          active: response.data.active_sources || 0,
          todayTotal: response.data.today_count || 0,
          totalVideos: response.data.total_videos || 0
        });
      } else {
        console.error('获取视频关键词统计失败:', response);
      }
    } catch (error: unknown) {
      console.error('获取视频关键词统计出错:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`获取统计数据失败: ${errorMessage}`);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchKeywords();
    fetchVideoStats();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);



  const handleAddKeyword = () => {
    addKeywordModalRef.current?.showModal();
  };

  const handleSearchKeyword = async () => {
    if (!searchKeyword.trim()) {
      toast.error('请输入关键词');
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      // 调试：检查 API 客户端状态
      console.log('TrendInsight API 客户端状态:', {
        trendInsightApi: !!trendInsightApi,
        searchVideos: !!trendInsightApi.searchVideos,
      });

      // 调用真实的 API
      const response = await trendInsightApi.searchVideos({
        keyword: searchKeyword.trim(),
        type: 'video',
      });


      if (response.code === 0 && response.data?.videos) {
        if (response.data.videos.length > 0) {
          setSearchResults(response.data.videos);
          setShowResults(true);
          toast.success(`找到 ${response.data.videos.length} 个相关视频`);
        } else {
          // API 成功但没有找到相关视频
          setSearchResults([]);
          setShowResults(true);
          toast.info('未找到相关视频，请尝试其他关键词');
        }
      } else {
        // API 返回非 200 状态码或数据格式异常
        const errorMessage = response.msg || `API 返回错误状态码: ${response.code}`;
        setSearchError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error('API 调用失败:', error);

      // 根据错误类型提供不同的错误信息和建议
      let errorMessage = '搜索失败';
      let suggestion = '';

      if (error instanceof Error) {
        if (error.name === 'NetworkError' || error.message.includes('fetch')) {
          errorMessage = '网络连接失败';
          suggestion = '请检查网络连接后重试';
        } else if (error.message.includes('timeout')) {
          errorMessage = '请求超时';
          suggestion = '服务器响应较慢，请稍后重试';
        } else {
          errorMessage = error.message;
          suggestion = '请稍后重试或联系技术支持';
        }
      } else {
        const errorObj = error as Record<string, unknown>;
        
        if (typeof errorObj.status === 'number') {
          if (errorObj.status === 401) {
            errorMessage = '认证失败';
            suggestion = '请检查登录状态';
          } else if (errorObj.status === 403) {
            errorMessage = '权限不足';
            suggestion = '请联系管理员获取权限';
          } else if (errorObj.status >= 500) {
            errorMessage = '服务器内部错误';
            suggestion = '服务器暂时不可用，请稍后重试';
          }
        } else {
          errorMessage = '未知错误';
          suggestion = '请稍后重试或联系技术支持';
        }
      }

      setSearchError(`${errorMessage}: ${suggestion}`);
      toast.error(`${errorMessage}，${suggestion}`);
    } finally {
      setIsSearching(false);
    }
  };

  const handleConfirmAddKeyword = async () => {
    if (!searchKeyword.trim()) {
      toast.error('请输入关键词');
      return;
    }

    try {
      setIsAddingKeyword(true);

      // 调用API创建关键词
      const response = await trendInsightApi.upsertVideoKeywords({
        keyword: searchKeyword.trim()
      });

      if (response.code === 0) {
        toast.success(`关键词"${searchKeyword}"添加成功！`);
        // 重新获取关键词列表
        await fetchKeywords();
        // 重新获取统计数据
        await fetchVideoStats();
        resetAddKeywordModal();
      } else {
        toast.error(`添加失败: ${response.msg || '未知错误'}`);
      }
    } catch (error: unknown) {
      console.error('添加关键词失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`添加关键词失败: ${errorMessage}`);
    } finally {
      setIsAddingKeyword(false);
    }
  };

  const resetAddKeywordModal = () => {
    setSearchKeyword('');
    setSearchResults([]);
    setShowResults(false);
    setSearchError(null);
    addKeywordModalRef.current?.close();
  };

  const handleDeleteKeywordWithConfirm = (keyword: UserKeywordInfo) => {
    // 检查是否有有效的ID
    if (!keyword.id) {
      toast.error('关键词ID无效，无法删除');
      return;
    }

    // 设置要删除的关键词并显示确认模态框
    setKeywordToDelete(keyword);
    deleteConfirmModalRef.current?.showModal();
  };

  const handleConfirmDelete = () => {
    if (keywordToDelete?.id) {
      handleDeleteKeyword(keywordToDelete.id);
      setKeywordToDelete(null);
      deleteConfirmModalRef.current?.close();
    }
  };

  const handleCancelDelete = () => {
    setKeywordToDelete(null);
    deleteConfirmModalRef.current?.close();
  };

  const handleDeleteKeyword = async (keywordId: string) => {
    const keyword = keywords.find(k => k.id === keywordId);
    if (!keyword) {
      toast.error('关键词不存在');
      return;
    }

    try {
      setIsDeleting(true);

      // 调用API删除关键词
      const response = await trendInsightApi.deleteUserKeyword(keywordId);

      if (response.code === 0) {
        // 从本地状态中移除
        setKeywords(prev => prev.filter(k => k.id !== keywordId));
        // 重新获取最新的统计数据
        await fetchVideoStats();
        toast.success(`关键词"${keyword.trend_keyword?.keyword || '未知'}"已删除`);
      } else {
        toast.error(`删除失败: ${response.msg || '未知错误'}`);
      }
    } catch (error: unknown) {
      console.error('删除关键词失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`删除关键词失败: ${errorMessage}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleBatchSync = async () => {
    setIsSyncing(true);
    batchSyncModalRef.current?.close();

    try {
      // 重新获取最新的关键词数据
      await fetchKeywords();
      
      // 重新获取最新的统计数据
      await fetchVideoStats();

      // 这里可以添加更多的同步逻辑，比如：
      // 1. 检查每个关键词的最新视频
      // 2. 更新统计数据
      // 3. 同步到灵感收件箱等

      toast.success('批量同步完成！数据已更新');
    } catch (error: unknown) {
      console.error('批量同步失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`同步失败: ${errorMessage}`);
    } finally {
      setIsSyncing(false);
    }
  };

  const viewKeywordVideos = (keyword: UserKeywordInfo) => {
    // 由于没有今日新增视频数据，暂时显示提示信息
    toast.info(`关键词"${keyword.trend_keyword?.keyword || '未知'}"的视频查看功能开发中`);
    // keywordVideosModalRef.current?.showModal();
  };

  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">行业关键词监控</h1>
        <p className="text-base-content opacity-60">监控行业热门关键词，发现相关视频内容</p>
      </header>

      {/* 操作区域 */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <Button
          className="bg-black text-white hover:bg-gray-800"
          onClick={handleAddKeyword}
          disabled={isLoading}
        >
          <i className="fas fa-plus mr-2"></i>添加关键词
        </Button>
        <div className="flex items-center gap-3">
          <span className="text-sm opacity-60">
            最后更新：{keywords.length > 0 ?
              (keywords[0]?.updateTime ? formatDateTime(new Date(keywords[0].updateTime)) : '未知') :
              '暂无数据'}
          </span>
          <Button
            size="sm"
            className="btn-outline"
            onClick={() => batchSyncModalRef.current?.showModal()}
            loading={isSyncing}
            disabled={isLoading}
          >
            <i className="fas fa-sync mr-2"></i>批量同步
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="alert alert-error mb-6">
          <i className="fas fa-exclamation-triangle"></i>
          <div className="flex-1">
            <div>
              <div className="font-bold">数据加载失败</div>
              <div className="text-sm">{error}</div>
            </div>
          </div>
          <Button
            size="sm"
            className="btn-outline btn-sm"
            onClick={fetchKeywords}
            loading={isLoading}
            disabled={isLoading}
          >
            <i className="fas fa-redo mr-1"></i>重试
          </Button>
        </div>
      )}

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-primary">
            <i className="fas fa-search text-3xl"></i>
          </div>
          <div className="stat-title">监控关键词</div>
          <div className="stat-value text-primary">{videoStats.total}</div>
          <div className="stat-desc">正在监控</div>
        </div>
        
        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-secondary">
            <i className="fas fa-video text-3xl"></i>
          </div>
          <div className="stat-title">发现视频</div>
          <div className="stat-value text-secondary">{videoStats.totalVideos}</div>
          <div className="stat-desc">总计</div>
        </div>
        
        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-info">
            <i className="fas fa-clock text-3xl"></i>
          </div>
          <div className="stat-title">今日新增</div>
          <div className="stat-value text-info">{videoStats.todayTotal}</div>
          <div className="stat-desc">新视频</div>
        </div>
      </div>

      {/* 关键词监控列表 */}
      <Card className="bg-base-100 shadow-md">
        <Card.Body>
          <h3 className="text-lg font-bold mb-4">关键词监控列表</h3>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center gap-3">
                <span className="loading loading-spinner loading-lg"></span>
                <p className="text-sm opacity-60">正在加载关键词列表...</p>
              </div>
            </div>
          ) : keywords.length === 0 && !error ? (
            <div className="text-center py-12">
              <i className="fas fa-search text-4xl text-base-300 mb-4"></i>
              <p className="text-base-content opacity-60 mb-4">
                {error ? '数据加载失败' : '暂无关键词监控'}
              </p>
              <p className="text-sm text-base-content opacity-40 mb-4">
                开始添加关键词来监控相关视频内容
              </p>
              <Button
                className="bg-black text-white hover:bg-gray-800"
                onClick={handleAddKeyword}
                disabled={isLoading}
              >
                <i className="fas fa-plus mr-2"></i>添加第一个关键词
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {keywords.map((keyword, index) => (
              <div key={keyword.id || `keyword-${index}`} className="border border-base-300 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start gap-3 mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-base">{keyword.trend_keyword?.keyword || '未知关键词'}</h4>
                  </div>
                  <div className="dropdown dropdown-end">
                    <div tabIndex={0} role="button" className="btn btn-ghost btn-xs">
                      <i className="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                      <li>
                        <a onClick={() => handleDeleteKeywordWithConfirm(keyword)}>
                          <i className="fas fa-trash mr-2"></i>删除
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-center mb-3">
                  <div>
                    <div className="text-lg font-semibold">{keyword.today_count || 0}</div>
                    <div className="text-xs opacity-60">今日新增</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold">{keyword.trend_keyword?.video_count || 0}</div>
                    <div className="text-xs opacity-60">累计视频</div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    size="xs" 
                    className="btn-outline flex-1"
                    onClick={() => viewKeywordVideos(keyword)}
                    disabled={true} // 暂时禁用，因为没有今日新增数据
                  >
                    查看今日新增
                  </Button>
                </div>
              </div>
            ))}
            
            {/* 添加关键词占位卡片 */}
            <div className="border-2 border-dashed border-base-300 rounded-lg p-4 flex items-center justify-center min-h-[160px]">
              <div className="text-center">
                <i className="fas fa-plus text-2xl text-base-300 mb-2"></i>
                <p className="text-sm opacity-60 mb-2">添加更多关键词</p>
                <Button size="sm" className="btn-outline" onClick={handleAddKeyword}>
                  添加关键词
                </Button>
              </div>
            </div>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* 添加关键词模态框 */}
      <Modal ref={addKeywordModalRef} containerClasses="modal-bottom sm:modal-middle" className="sm:max-w-2xl">
        <Modal.Header className="text-lg font-bold mb-6">添加监控关键词</Modal.Header>
        <Modal.Body>
          {!showResults ? (
            <div className="space-y-6">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">关键词</span>
                </label>
                <div className="join w-full">
                  <input
                    type="text"
                    className="input input-bordered join-item flex-1"
                    placeholder="请输入要监控的关键词"
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearchKeyword()}
                    disabled={isSearching}
                  />
                  <Button
                    className="join-item bg-black text-white hover:bg-gray-800"
                    onClick={handleSearchKeyword}
                    loading={isSearching}
                    disabled={isSearching}
                  >
                    <i className="fas fa-search mr-2"></i>
                    {isSearching ? '搜索中...' : '搜索'}
                  </Button>
                </div>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">平台</span>
                </label>
                <select className="select select-bordered w-full" disabled>
                  <option selected>抖音</option>
                </select>
              </div>

              {isSearching && (
                <div className="flex items-center justify-center py-8">
                  <div className="flex flex-col items-center gap-3">
                    <span className="loading loading-spinner loading-lg"></span>
                    <p className="text-sm opacity-60">正在搜索相关视频...</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <h4 className="font-medium text-lg">"{searchKeyword}"相关视频前 10 条</h4>
                <Button size="sm" className="btn-outline" onClick={() => setShowResults(false)}>
                  <i className="fas fa-arrow-left mr-2"></i>重新搜索
                </Button>
              </div>

              {searchError && (
                <div className="alert alert-error">
                  <i className="fas fa-exclamation-triangle"></i>
                  <div className="flex-1">
                    <span>{searchError}</span>
                  </div>
                  <Button
                    size="sm"
                    className="btn-outline btn-sm"
                    onClick={handleSearchKeyword}
                    loading={isSearching}
                    disabled={isSearching}
                  >
                    <i className="fas fa-redo mr-1"></i>重试
                  </Button>
                </div>
              )}

              {searchResults.length > 0 ? (
                <>
                  <div className="space-y-3 border border-base-300 rounded-lg p-4 max-h-80 overflow-y-auto">
                    {searchResults.slice(0, 10).map((video, index) => (
                      <div key={video.itemId || `video-${index}`} className="border border-base-300 rounded-lg p-3 hover:bg-base-50 transition-colors">
                        <div className="flex gap-3">
                          <img
                            src={video.thumbnail || '/placeholder-video.jpg'}
                            alt={video.title}
                            className="w-24 h-16 object-cover rounded flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <h5 className="font-medium mb-2 line-clamp-2">{video.title}</h5>
                            <div className="flex flex-wrap items-center gap-2 text-xs opacity-60 mb-2">
                              <span>
                                <i className="fas fa-user mr-1"></i>
                                {video.nickname || '未知作者'}
                              </span>
                              <span>
                                <i className="fas fa-calendar mr-1"></i>
                                {formatTime(video.createTime)}
                              </span>
                              <span>
                                <i className="fas fa-heart mr-1"></i>
                                {formatNumber(video.likes)}
                              </span>
                              <span>
                                <i className="fas fa-users mr-1"></i>
                                {formatNumber(video.fans)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-end">
                    <Button
                      className="bg-black text-white hover:bg-gray-800"
                      onClick={handleConfirmAddKeyword}
                      disabled={isAddingKeyword}
                      loading={isAddingKeyword}
                    >
                      <i className="fas fa-plus mr-2"></i>{isAddingKeyword ? '添加中...' : '确认添加监控'}
                    </Button>
                  </div>
                </>
              ) : !searchError && (
                <div className="text-center py-8">
                  <i className="fas fa-search text-4xl text-base-300 mb-4"></i>
                  <p className="text-base-content opacity-60 mb-4">未找到相关视频</p>
                  <p className="text-sm text-base-content opacity-40 mb-4">
                    请尝试使用其他关键词或检查关键词拼写
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button
                      size="sm"
                      className="btn-outline"
                      onClick={() => setShowResults(false)}
                    >
                      <i className="fas fa-arrow-left mr-2"></i>重新搜索
                    </Button>
                    <Button
                      size="sm"
                      className="bg-black text-white hover:bg-gray-800"
                      onClick={handleConfirmAddKeyword}
                      disabled={isAddingKeyword}
                      loading={isAddingKeyword}
                    >
                      <i className="fas fa-plus mr-2"></i>{isAddingKeyword ? '添加中...' : '仍然添加监控'}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
      </Modal>

      {/* 关键词视频列表模态框 */}
      <Modal ref={keywordVideosModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Header className="text-xl font-bold mb-4 flex items-center gap-2">
          <i className="fas fa-calendar-plus text-primary"></i>
          今日新增视频
        </Modal.Header>
        <Modal.Body>
          <div className="overflow-x-auto max-h-96 overflow-y-auto">
            <table className="table table-sm table-zebra">
              <thead className="bg-base-200 sticky top-0">
                <tr>
                  <th className="w-16">#</th>
                  <th>标题</th>
                  <th>作者</th>
                  <th>发布时间</th>
                </tr>
              </thead>
              <tbody>
                {searchResults.slice(0, 10).map((video, index) => (
                  <tr key={video.itemId || `video-row-${index}`}>
                    <td>{index + 1}</td>
                    <td className="font-medium">{video.title}</td>
                    <td>{video.nickname || '未知作者'}</td>
                    <td>{formatTime(video.createTime)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog">
            <Button>关闭</Button>
          </form>
        </Modal.Actions>
      </Modal>

      {/* 批量同步确认模态框 */}
      <Modal ref={batchSyncModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Header className="text-lg font-bold mb-4">批量同步确认</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>确定要同步所有关键词的最新数据吗？</p>
            <div className="bg-base-200 p-3 rounded-lg">
              <div className="text-sm opacity-80">
                <p>• 将检查所有关键词的最新视频</p>
                <p>• 监控到的数据将自动导入至灵感收件箱</p>
                <p>• 更新关键词监控数据</p>
                <p>• 预计需要 1-2 分钟完成</p>
              </div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog" className="flex gap-2">
            <Button onClick={() => batchSyncModalRef.current?.close()}>取消</Button>
            <Button
              className="bg-black text-white hover:bg-gray-800"
              onClick={handleBatchSync}
              disabled={isSyncing}
              loading={isSyncing}
            >
              {isSyncing ? '同步中...' : '确认同步'}
            </Button>
          </form>
        </Modal.Actions>
      </Modal>

      {/* 删除确认模态框 */}
      <Modal ref={deleteConfirmModalRef} className="w-11/12 max-w-md">
        <h3 className="text-lg font-bold mb-4">确认删除</h3>
        <div className="space-y-4">
          <p>
            您确定要删除关键词 <span className="font-medium">{keywordToDelete?.trend_keyword?.keyword || '未知关键词'}</span> 吗？
          </p>
          <div className="bg-base-200 p-3 rounded-lg">
            <div className="text-sm opacity-80">
              <p>• 删除后将停止监控该关键词的更新</p>
              <p>• 已收集的历史数据将被保留</p>
              <p>• 此操作无法撤销</p>
            </div>
          </div>
        </div>
        <div className="modal-action">
          <div className="flex gap-2">
            <Button
              className="btn"
              onClick={handleCancelDelete}
            >
              取消
            </Button>
            <Button
              className="btn btn-error text-white"
              onClick={handleConfirmDelete}
              disabled={isDeleting}
              loading={isDeleting}
            >
              {isDeleting ? '删除中...' : '确认删除'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default KeywordMonitoring;
