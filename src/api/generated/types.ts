/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/client/trendinsight/searchVideos": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 搜索巨量引擎平台视频
         * @description 根据关键词和筛选条件搜索巨量引擎平台的视频内容，支持多维度搜索参数
         */
        post: operations["searchVideos"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/searchAuthors": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 搜索巨量引擎平台创作者
         * @description 根据关键词搜索巨量引擎平台的创作者/达人信息
         */
        post: operations["searchAuthors"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/getUserVideoKeywords": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户视频关键词监控列表
         * @description 分页获取当前用户的视频关键词监控列表，包含用户个性化配置和关键词详细信息
         */
        get: operations["getUserVideoKeywords"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/getUserAuthorKeywords": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户作者关键词监控列表
         * @description 分页获取当前用户的作者关键词监控列表，包含用户个性化配置和关键词详细信息
         */
        get: operations["getUserAuthorKeywords"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/delUserKeywordParId/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * 删除用户关键词关联
         * @description 删除用户的关键词监控关联，执行软删除操作
         */
        delete: operations["delUserKeywordParId"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/upsertVideoKeywords": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 创建或更新视频关键词
         * @description 创建或更新单个视频关键词到 MediaCrawler 服务，并建立用户关键词关联。操作流程：1) 调用 MediaCrawler 服务创建/更新关键词；2) 在本地数据库创建用户关键词关联记录。
         */
        post: operations["upsertVideoKeywords"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/upsertAuthorKeywords": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 创建或更新作者关键词
         * @description 通过单个用户ID创建或更新作者关键词到 MediaCrawler 服务，并建立用户关键词关联。操作流程：1) 调用 MediaCrawler 服务创建/更新作者信息；2) 在本地数据库创建用户关键词关联记录。
         */
        post: operations["upsertAuthorKeywords"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/getVideoKeywordStats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户视频关键词统计信息
         * @description 获取当前用户的视频关键词统计信息，包括每个关键词关联的视频总数和今日新增视频数
         */
        get: operations["getVideoKeywordStats"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/getAuthorKeywordStats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户作者关键词统计信息
         * @description 获取当前用户的作者关键词统计信息，包括每个关键词关联的作者总数和今日新增作者数
         */
        get: operations["getAuthorKeywordStats"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/config/getConfig": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取TrendInsight配置信息
         * @description 获取当前TrendInsight服务的配置信息，包括基础URL、超时设置、重试次数和API参数说明
         */
        get: operations["getConfig"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/config/testConnection": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 测试TrendInsight连接
         * @description 测试与MediaCrawler服务的连接状态，验证配置是否正确
         */
        post: operations["testConnection"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/trendinsight/config/getApiInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取API信息
         * @description 获取TrendInsight API的详细信息，包括版本、端点列表和参数说明
         */
        get: operations["getApiInfo"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getSyncRecords": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取抖音同步记录
         * @description 获取用户的抖音同步记录列表，支持分页查询
         */
        get: operations["getSyncRecords"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getSyncStats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户视频统计信息
         * @description 获取用户的视频统计信息，基于 user_inbox_video_related 表统计，包括总记录数、按来源类型分组统计和最新记录时间
         */
        get: operations["getSyncStats"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getSyncRecordRelated": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取同步记录关联视频
         * @description 获取指定同步记录的关联视频列表，支持分页查询
         */
        get: operations["getSyncRecordRelated"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getSyncRecordRelatedList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户所有同步记录的关联视频列表
         * @description 获取当前用户所有同步记录的关联视频列表，支持分页查询，包含完整的抖音视频详细信息
         */
        get: operations["getSyncRecordRelatedList"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getVideoDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取抖音视频详情
         * @description 通过aweme_id获取抖音视频的详细信息，包括视频元数据、统计数据和作者信息
         */
        get: operations["getVideoDetail"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/syncCollects": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 同步抖音收藏夹
         * @description 同步用户的抖音收藏夹数据，包括收藏夹信息和视频信息
         */
        post: operations["syncCollects"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/bindDouyinCookie": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 绑定抖音Cookie
         * @description 绑定用户的抖音Cookie，用于后续的数据同步操作
         */
        post: operations["bindDouyinCookie"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getVideoRelatedList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户收件箱视频关联记录列表
         * @description 查询当前用户的收件箱视频关联记录列表，支持过滤、排序和分页
         */
        get: operations["getVideoRelatedList"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getVideoRelatedStats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户收件箱视频关联记录统计信息
         * @description 获取当前用户在 user_inbox_video_related 表中的视频关联记录统计信息
         */
        get: operations["getVideoRelatedStats"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/addVideoToAssets": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 添加视频到素材库
         * @description 将 user_inbox_video_related 表中状态为 PENDING 的视频数据添加到 assets 表中，支持从收藏夹、关键词、作者等来源添加视频素材
         */
        post: operations["addVideoToAssets"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client/user/douyin/getAuthorVideos": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据趋势洞察用户ID查询作者发布的视频
         * @description 根据trendinsight_user_id和user_uuid查询user_inbox_video_related表获取数据，然后关联查询douyin_aweme表获取视频详细信息。支持分页查询，按发布时间倒序排列。适用于查询用户收件箱中特定作者的视频数据。
         */
        get: operations["getAuthorVideos"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        VideoSearchRequest: {
            /**
             * @description 搜索关键词
             * @example 人工智能
             */
            keyword: string;
            /**
             * @description 搜索类型：video-视频搜索，author-作者搜索
             * @default video
             * @example video
             * @enum {string}
             */
            type: "video" | "author";
            /**
             * @description 作者ID列表，可选参数
             * @example [
             *       "author1",
             *       "author2"
             *     ]
             */
            author_ids?: string[];
            /**
             * @description 分类ID，可选参数
             * @example 1
             */
            category_id?: string;
            /**
             * @description 日期类型：0-全部时间，1-最近7天，2-最近30天，3-最近90天
             * @example 1
             */
            date_type?: number;
            /**
             * @description 标签类型：0-全部标签，1-原创，2-转发
             * @example 1
             */
            label_type?: number;
            /**
             * @description 时长类型：0-全部时长，1-短视频，2-中等时长，3-长视频
             * @example 2
             */
            duration_type?: number;
        };
        CreatorSearchRequest: {
            /**
             * @description 搜索关键词
             * @example 科技博主
             */
            keyword: string;
            /**
             * @description 搜索类型：author-作者搜索，video-视频搜索
             * @default author
             * @example author
             * @enum {string}
             */
            type: "author" | "video";
            /**
             * @description 返回数量，可选参数
             * @example 20
             */
            total?: number;
        };
        VideoSearchResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 视频搜索成功
             */
            msg?: string;
            data?: components["schemas"]["TrendInsightVideoSearchResponseSchema"];
        };
        AuthorSearchResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 创作者搜索成功
             */
            msg?: string;
            data?: components["schemas"]["TrendInsightAuthorSearchResponseSchema"];
        };
        ConfigResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 配置信息获取成功
             */
            msg?: string;
            data?: {
                /**
                 * @description MediaCrawler基础URL
                 * @example http://localhost:8000
                 */
                base_url?: string;
                /**
                 * @description 请求超时时间
                 * @example 30s
                 */
                timeout?: string;
                /**
                 * @description 重试次数
                 * @example 3
                 */
                retry_count?: number;
                /** @description API端点配置 */
                endpoints?: {
                    /** @example /api/v1/trendinsight/search/videos */
                    search_videos?: string;
                    /** @example /api/v1/trendinsight/search/creators */
                    search_creators?: string;
                };
                /** @description 参数配置说明 */
                parameters?: {
                    video_search?: {
                        /** @example [
                         *       "keyword"
                         *     ] */
                        required?: string[];
                        /** @example [
                         *       "type",
                         *       "author_ids",
                         *       "category_id",
                         *       "date_type",
                         *       "label_type",
                         *       "duration_type"
                         *     ] */
                        optional?: string[];
                        /** @example {
                         *       "0": "全部时间",
                         *       "1": "最近7天",
                         *       "2": "最近30天",
                         *       "3": "最近90天"
                         *     } */
                        date_types?: {
                            [key: string]: string;
                        };
                        /** @example {
                         *       "0": "全部标签",
                         *       "1": "原创",
                         *       "2": "转发"
                         *     } */
                        label_types?: {
                            [key: string]: string;
                        };
                        /** @example {
                         *       "0": "全部时长",
                         *       "1": "短视频",
                         *       "2": "中等时长",
                         *       "3": "长视频"
                         *     } */
                        duration_types?: {
                            [key: string]: string;
                        };
                    };
                    creator_search?: {
                        /** @example [
                         *       "keyword"
                         *     ] */
                        required?: string[];
                        /** @example [
                         *       "type",
                         *       "total"
                         *     ] */
                        optional?: string[];
                        limits?: {
                            /** @example 1 */
                            total_min?: number;
                            /** @example 100 */
                            total_max?: number;
                        };
                    };
                };
            };
        };
        ConnectionTestResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 连接测试成功
             */
            msg?: string;
            data?: {
                /**
                 * @description 连接状态
                 * @example true
                 */
                connected?: boolean;
                /**
                 * @description 测试的基础URL
                 * @example http://localhost:8000
                 */
                base_url?: string;
                /**
                 * @description HTTP状态码
                 * @example 200
                 */
                status_code?: number;
                /**
                 * @description 响应时间
                 * @example < 1s
                 */
                response_time?: string;
                /**
                 * @description 测试时间
                 * @example 2024-06-19 15:30:45
                 */
                test_time?: string;
                /**
                 * @description 错误信息（连接失败时）
                 * @example connection refused
                 */
                error?: string;
            };
        };
        ApiInfoResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example API信息获取成功
             */
            msg?: string;
            data?: {
                /**
                 * @description 服务名称
                 * @example TrendInsight API
                 */
                service_name?: string;
                /**
                 * @description API版本
                 * @example v1.0.0
                 */
                version?: string;
                /**
                 * @description 服务描述
                 * @example 巨量引擎平台数据搜索API
                 */
                description?: string;
                /** @description 端点列表 */
                endpoints?: {
                    /** @description 端点路径 */
                    path?: string;
                    /** @description HTTP方法 */
                    method?: string;
                    /** @description 端点描述 */
                    description?: string;
                    /** @description 参数说明 */
                    parameters?: {
                        [key: string]: string;
                    };
                }[];
                /** @description 错误码说明 */
                error_codes?: {
                    [key: string]: string;
                };
            };
        };
        ErrorResponse: {
            /**
             * @description 错误状态码
             * @example 400
             */
            code?: number;
            /**
             * @description 错误消息
             * @example 请求参数错误
             */
            msg?: string;
            /** @description 错误详细信息 */
            data?: Record<string, never> | null;
        };
        ValidationErrorResponse: {
            /**
             * @description 验证错误状态码
             * @example 422
             */
            code?: number;
            /**
             * @description 验证错误消息
             * @example 参数验证失败
             */
            msg?: string;
            /** @description 验证错误详情 */
            data?: {
                /** @description 详细错误信息 */
                detail?: {
                    /** @description 错误位置 */
                    loc?: (string | number)[];
                    /** @description 错误消息 */
                    msg?: string;
                    /** @description 错误类型 */
                    type?: string;
                }[];
            };
        };
        GetTaskListResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取任务列表成功
             */
            msg?: string;
            data?: {
                /** @description 任务列表 */
                tasks?: components["schemas"]["TaskListItem"][];
                /**
                 * @description 总任务数量
                 * @example 25
                 */
                total?: number;
                /**
                 * @description 当前页码
                 * @example 1
                 */
                page?: number;
                /**
                 * @description 每页数量
                 * @example 10
                 */
                page_size?: number;
                /**
                 * @description 总页数
                 * @example 3
                 */
                total_pages?: number;
                /**
                 * @description 是否有更多数据
                 * @example true
                 */
                has_more?: boolean;
            };
        };
        /** @description 增强的任务列表项，包含用户关键词个性化信息 */
        TaskListItem: {
            /**
             * @description 任务ID
             * @example douyin_1640995200_12345678
             */
            task_id?: string;
            /**
             * @description 任务名称
             * @example douyin_video_人工智能
             */
            task_name?: string;
            /**
             * @description 任务描述
             * @example 抖音平台人工智能关键词视频爬取任务
             */
            description?: string;
            /**
             * @description 爬取平台
             * @example douyin
             * @enum {string}
             */
            platform?: "xiaohongshu" | "weibo" | "tieba" | "bilibili" | "douyin" | "kuaishou" | "zhihu" | "trendinsight";
            /**
             * @description 爬虫类型：search-搜索爬虫，detail-详情爬虫，comment-评论爬虫，user-用户爬虫，creator-创作者爬虫，live-直播爬虫，homefeed-首页推荐
             * @example search
             * @enum {string}
             */
            crawler_type?: "search" | "detail" | "comment" | "user" | "creator" | "live" | "homefeed";
            /**
             * @description 任务状态
             * @example running
             * @enum {string}
             */
            status?: "PENDING" | "RUNNING" | "PROCESSING" | "PAUSED" | "COMPLETED" | "SUCCESS" | "FAILED" | "ERROR" | "CANCELLED";
            /**
             * @description 任务进度（百分比）
             * @example 65
             */
            progress?: number;
            /**
             * @description 任务优先级
             * @example 80
             */
            priority?: number;
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2023-12-31T23:59:59Z
             */
            created_at?: string;
            /**
             * Format: date-time
             * @description 开始时间
             * @example 2024-01-01T00:00:00Z
             */
            started_at?: string | null;
            /**
             * Format: date-time
             * @description 完成时间
             * @example 2024-01-01T01:00:00Z
             */
            completed_at?: string | null;
            /**
             * Format: date-time
             * @description 失败时间
             * @example null
             */
            failed_at?: string | null;
            /**
             * @description 总项目数
             * @example 100
             */
            total_items?: number;
            /**
             * @description 成功项目数
             * @example 65
             */
            success_items?: number;
            /**
             * @description 失败项目数
             * @example 2
             */
            failed_items?: number;
            /**
             * @description 错误消息
             * @example
             */
            error_message?: string | null;
            /**
             * @description 实际执行时长（秒）
             * @example 3600
             */
            actual_duration?: number;
            /**
             * @description 用户自定义关键词别名
             * @example AI技术
             */
            keyword_alias?: string | null;
            /**
             * @description 关键词显示名称（优先使用别名）
             * @example AI技术
             */
            keyword_display_name?: string;
            /**
             * @description 用户关键词优先级
             * @example 85
             */
            keyword_priority?: number;
            /**
             * @description 是否为收藏关键词
             * @example true
             */
            keyword_is_favorite?: boolean;
            /**
             * @description 关键词使用次数
             * @example 15
             */
            keyword_usage_count?: number;
            /**
             * @description 用户自定义关键词分类
             * @example 技术类
             */
            keyword_category?: string | null;
        };
        UserKeywordListResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取关键词监控列表成功
             */
            msg?: string;
            data?: {
                /** @description 关键词监控列表 */
                keywords?: components["schemas"]["UserKeywordInfo"][];
                /**
                 * @description 总数量
                 * @example 25
                 */
                total?: number;
                /**
                 * @description 当前页码
                 * @example 1
                 */
                page?: number;
                /**
                 * @description 每页数量
                 * @example 10
                 */
                page_size?: number;
                /**
                 * @description 总页数
                 * @example 3
                 */
                total_pages?: number;
                /**
                 * @description 是否有更多数据
                 * @example true
                 */
                has_more?: boolean;
            };
        };
        DeleteUserKeywordRequest: {
            /**
             * @description 要删除的关键词关联UUID
             * @example 1234567890abcdef1234567890abcdef
             */
            keyword_uuid: string;
        };
        DeleteUserKeywordResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 删除关键词成功
             */
            msg?: string;
            data?: {
                /**
                 * @description 被删除的关键词UUID
                 * @example 1234567890abcdef1234567890abcdef
                 */
                keyword_uuid?: string;
                /**
                 * @description 关键词ID
                 * @example 123
                 */
                keyword_id?: number;
                /**
                 * @description 关键词显示名称
                 * @example 测试关键词
                 */
                display_name?: string;
                /**
                 * Format: date-time
                 * @description 删除时间
                 * @example 2024-01-01T12:00:00Z
                 */
                deleted_at?: string;
                /**
                 * @description 操作结果消息
                 * @example 关键词删除成功
                 */
                message?: string;
            };
        };
        GetUserAuthorKeywordListResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取author关键词监控列表成功
             */
            msg?: string;
            data?: {
                /** @description 作者关键词监控列表 */
                list?: components["schemas"]["UserAuthorKeywordInfo"][];
                /**
                 * @description 总数量
                 * @example 15
                 */
                total?: number;
                /**
                 * @description 当前页码
                 * @example 1
                 */
                page?: number;
                /**
                 * @description 每页数量
                 * @example 10
                 */
                page_size?: number;
                /**
                 * @description 总页数
                 * @example 2
                 */
                total_pages?: number;
                /**
                 * @description 是否有更多数据
                 * @example true
                 */
                has_more?: boolean;
            };
        };
        GetUserKeywordListResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取关键词监控列表成功
             */
            msg?: string;
            data?: {
                /** @description 关键词监控列表 */
                list?: components["schemas"]["UserKeywordInfo"][];
                /**
                 * @description 总数量
                 * @example 25
                 */
                total?: number;
                /**
                 * @description 当前页码
                 * @example 1
                 */
                page?: number;
                /**
                 * @description 每页数量
                 * @example 10
                 */
                page_size?: number;
                /**
                 * @description 总页数
                 * @example 3
                 */
                total_pages?: number;
                /**
                 * @description 是否有更多数据
                 * @example true
                 */
                has_more?: boolean;
            };
        };
        /** @description 用户关键词监控信息 */
        UserKeywordInfo: {
            /**
             * @description 主键
             * @example 1234567890abcdef1234567890abcdef
             */
            id?: string;
            /**
             * @description 用户UUID
             * @example abcdef1234567890abcdef1234567890
             */
            user_uuid?: string;
            /**
             * @description 关键词ID (外键关联TrendInsightKeyword)
             * @example 1
             */
            keyword_id?: number;
            /**
             * @description 爬取类型
             * @default video
             * @example video
             * @enum {string}
             */
            type: "video" | "author";
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2024-01-01T00:00:00Z
             */
            createTime?: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2024-01-15T10:30:00Z
             */
            updateTime?: string;
            /**
             * @description 是否删除
             * @example false
             */
            is_deleted?: boolean;
            /**
             * @description 该关键词当天的数据数量（基于 create_time）
             * @example 15
             */
            today_count?: number;
            /**
             * @description 该关键词的总数据数量（不限制时间）
             * @example 150
             */
            total_count?: number;
            /** @description 关联的关键词详细信息 (当联表查询时返回) */
            trend_keyword?: {
                /**
                 * @description 关键词ID
                 * @example 1
                 */
                id?: number;
                /**
                 * @description 关键词文本
                 * @example 人工智能
                 */
                keyword?: string;
                /**
                 * @description 关键词哈希值
                 * @example 5d41402abc4b2a76b9719d911017c592
                 */
                keyword_hash?: string;
                /**
                 * Format: float
                 * @description 热度评分 (float32)
                 * @example 85.5
                 */
                heat_score?: number;
                /**
                 * Format: int32
                 * @description 搜索量 (int32)
                 * @example 100000
                 */
                search_volume?: number;
                /**
                 * @description 趋势方向
                 * @example rising
                 * @enum {string}
                 */
                trend_direction?: "rising" | "falling" | "stable" | "volatile";
                /**
                 * @description 关键词分类
                 * @example 科技
                 */
                category?: string | null;
                /**
                 * @description 关键词标签（JSON格式）
                 * @example ["AI", "机器学习", "深度学习"]
                 */
                tags?: string | null;
                /**
                 * Format: int32
                 * @description 相关视频数 (int32)
                 * @example 500
                 */
                video_count?: number;
                /**
                 * Format: int32
                 * @description 相关作者数 (int32)
                 * @example 100
                 */
                author_count?: number;
                /**
                 * Format: date
                 * @description 统计日期
                 * @example 2024-01-15
                 */
                date?: string;
                /**
                 * @description 爬取时间 (RFC3339格式字符串)
                 * @example 2024-01-15T10:30:00Z
                 */
                crawl_time?: string;
                /**
                 * @description 原始数据（JSON格式）
                 * @example {"source": "trendinsight", "platform": "douyin"}
                 */
                raw_data?: string | null;
                /**
                 * Format: date-time
                 * @description 关键词创建时间
                 * @example 2024-01-01T00:00:00Z
                 */
                created_at?: string;
                /**
                 * Format: date-time
                 * @description 关键词更新时间
                 * @example 2024-01-15T10:30:00Z
                 */
                updated_at?: string;
            } | null;
        };
        /** @description 用户作者关键词监控信息 */
        UserAuthorKeywordInfo: {
            /**
             * @description 主键
             * @example 1234567890abcdef1234567890abcdef
             */
            id?: string;
            /**
             * @description 用户UUID
             * @example abcdef1234567890abcdef1234567890
             */
            user_uuid?: string;
            /**
             * @description 关键词ID (外键关联TrendInsightAuthor)
             * @example 5
             */
            keyword_id?: number;
            /**
             * @description 爬取类型
             * @example author
             * @enum {string}
             */
            type?: "author";
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2024-01-01T00:00:00Z
             */
            created_at?: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2024-01-15T10:30:00Z
             */
            updated_at?: string;
            /**
             * @description 该作者关键词当天的数据数量（基于 create_time）
             * @example 8
             */
            today_count?: number;
            /**
             * @description 该作者关键词的总数据数量（不限制时间）
             * @example 85
             */
            total_count?: number;
            /** @description 关联的作者详细信息 */
            trend_author?: {
                /**
                 * @description 作者ID
                 * @example 5
                 */
                id?: number;
                /**
                 * @description 用户ID（平台唯一标识）
                 * @example douyin_123456
                 */
                user_id?: string;
                /**
                 * @description 抖音ID
                 * @example douyin_123456
                 */
                aweme_id?: string;
                /**
                 * @description 用户名
                 * @example 科技博主
                 */
                user_name?: string;
                /**
                 * @description 用户头像
                 * @example https://example.com/avatar.jpg
                 */
                user_head_logo?: string;
                /**
                 * @description 用户性别
                 * @example 男
                 */
                user_gender?: string;
                /**
                 * @description 用户位置
                 * @example 北京
                 */
                user_location?: string;
                /**
                 * @description 用户简介
                 * @example 专注AI技术分享
                 */
                user_introduction?: string;
                /**
                 * @description 作品数量(原始)
                 * @example 200
                 */
                item_count?: string;
                /**
                 * @description 粉丝数(原始)
                 * @example 10万
                 */
                fans_count?: string;
                /**
                 * @description 点赞数(原始)
                 * @example 100万
                 */
                like_count?: string;
                /**
                 * @description 作品数量(数值)
                 * @example 200
                 */
                item_count_int?: number;
                /**
                 * @description 粉丝数(数值)
                 * @example 100000
                 */
                fans_count_int?: number;
                /**
                 * @description 点赞数(数值)
                 * @example 1000000
                 */
                like_count_int?: number;
                /**
                 * @description 第一标签名称
                 * @example 科技
                 */
                first_tag_name?: string;
                /**
                 * @description 第二标签名称
                 * @example AI
                 */
                second_tag_name?: string;
                /**
                 * @description 粉丝里程碑创建时间
                 * @example 2024-01-01
                 */
                fans_milestone_create_time?: string;
                /**
                 * @description 抖音用户链接
                 * @example https://www.douyin.com/user/123456
                 */
                user_aweme_url?: string;
                /**
                 * @description 抖音头像图片
                 * @example https://example.com/aweme_pic.jpg
                 */
                aweme_pic?: string;
                /**
                 * @description 来源平台
                 * @example trendinsight
                 */
                platform?: string;
                /**
                 * @description 爬取时间戳
                 * @example 1640995200
                 */
                crawl_time?: number;
                /**
                 * @description 搜索来源关键字
                 * @example 科技博主
                 */
                source_keyword?: string;
                /**
                 * @description 原始数据(JSON格式)
                 * @example {"original_data": "..."}
                 */
                raw_data?: string;
                /**
                 * @description 是否被软删除
                 * @example false
                 */
                is_deleted?: boolean;
                /**
                 * Format: date-time
                 * @description 创建时间
                 * @example 2024-01-01T00:00:00.000000Z
                 */
                created_at?: string;
                /**
                 * Format: date-time
                 * @description 更新时间
                 * @example 2024-01-15T10:30:00.000000Z
                 */
                updated_at?: string;
            } | null;
            /**
             * @description 显示名称（用户名）
             * @example 科技博主
             */
            display_name?: string;
            /**
             * @description 通用显示名称（兼容性字段）
             * @example 科技博主
             */
            name?: string;
        };
        /** @description 视频关键词创建/更新请求 */
        UpsertVideoKeywordRequest: {
            /**
             * @description 视频关键词
             * @example 人工智能
             */
            keyword: string;
        };
        /** @description 作者关键词创建/更新请求 */
        UpsertAuthorKeywordRequest: {
            /**
             * @description 用户ID（平台唯一标识）
             * @example douyin_123456
             */
            user_id: string;
        };
        /** @description 简化的关键词创建/更新请求（类型通过路由确定） */
        UpsertKeywordRequestSimple: {
            /**
             * @description 关键词列表，支持批量操作
             * @example [
             *       "人工智能",
             *       "机器学习",
             *       "深度学习"
             *     ]
             */
            keywords: string[];
        };
        /** @description 关键词创建/更新请求 */
        UpsertKeywordRequest: {
            /**
             * @description 关键词列表，支持批量操作
             * @example [
             *       "人工智能",
             *       "机器学习",
             *       "深度学习"
             *     ]
             */
            keywords: string[];
            /**
             * @description 爬取类型，指定关键词的用途
             * @example video
             * @enum {string}
             */
            type: "video" | "author";
        };
        /** @description 视频关键词创建/更新响应 */
        UpsertVideoKeywordResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 视频关键词创建/更新成功
             */
            msg?: string;
            data?: {
                /**
                 * @description 创建/更新的关键词
                 * @example 人工智能
                 */
                keyword?: string;
                /** @description 用户关键词关联记录 */
                user_keyword?: {
                    /** @description 关联记录UUID */
                    id?: string;
                    /** @description 关键词ID */
                    keyword_id?: number;
                    /**
                     * @description 关键词类型
                     * @example video
                     */
                    type?: string;
                };
                /**
                 * @description 操作结果消息
                 * @example 视频关键词创建/更新成功
                 */
                message?: string;
            };
        };
        /** @description 作者关键词创建/更新响应 */
        UpsertAuthorKeywordResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 作者关键词创建/更新成功
             */
            msg?: string;
            data?: {
                /**
                 * @description 创建/更新的用户ID
                 * @example douyin_123456
                 */
                user_id?: string;
                /** @description 用户关键词关联记录 */
                user_keyword?: {
                    /** @description 关联记录UUID */
                    id?: string;
                    /** @description 关键词ID */
                    keyword_id?: number;
                    /**
                     * @description 关键词类型
                     * @example author
                     */
                    type?: string;
                };
                /**
                 * @description 操作结果消息
                 * @example 作者关键词创建/更新成功
                 */
                message?: string;
            };
        };
        /** @description 关键词创建/更新响应 */
        UpsertKeywordResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 关键词创建/更新处理完成
             */
            msg?: string;
            data?: {
                /**
                 * @description 成功处理的关键词数量
                 * @example 3
                 */
                success_count?: number;
                /**
                 * @description 失败的关键词数量
                 * @example 1
                 */
                failed_count?: number;
                /**
                 * @description 成功处理的关键词列表
                 * @example [
                 *       "人工智能",
                 *       "机器学习",
                 *       "深度学习"
                 *     ]
                 */
                success_keywords?: string[];
                /** @description 失败的关键词及错误信息 */
                failed_keywords?: {
                    /**
                     * @description 失败的关键词
                     * @example 无效关键词
                     */
                    keyword?: string;
                    /**
                     * @description 失败原因
                     * @example 关键词格式不正确
                     */
                    error?: string;
                }[];
            };
        };
        /** @description TrendInsight视频搜索响应模型 */
        TrendInsightVideoSearchResponseSchema: {
            /** @description 视频列表 */
            videos?: components["schemas"]["VideoItem"][];
            /**
             * @description 总数量
             * @example 100
             */
            total?: number;
            /**
             * @description 当前页码
             * @example 1
             */
            page?: number;
            /**
             * @description 每页大小
             * @example 20
             */
            page_size?: number;
            /**
             * @description 是否有更多
             * @example true
             */
            has_more?: boolean;
            /**
             * @description 搜索关键词
             * @example 人工智能
             */
            keyword?: string;
            /**
             * @description 分类ID
             * @example 1
             */
            category_id?: string;
            /**
             * @description 排序类型
             * @example hot
             */
            sort?: string;
            /**
             * @description 日期类型
             * @example 1
             */
            date_type?: number;
            /**
             * @description 标签类型
             * @example 1
             */
            label_type?: number;
            /**
             * @description 时长类型
             * @example 2
             */
            duration_type?: number;
            /**
             * @description 搜索结果总数
             * @example 156
             */
            total_count?: number;
            /**
             * @description 返回结果数量
             * @example 20
             */
            result_count?: number;
            /**
             * @description 搜索耗时(秒)
             * @example 0.5
             */
            search_time?: number;
            /**
             * @description 平均播放量
             * @example 1000
             */
            avg_play_count?: number;
            /**
             * @description 平均互动评分
             * @example 85.5
             */
            avg_engagement_score?: number;
            /** @description 平台分布 */
            platform_distribution?: {
                [key: string]: number;
            };
            /** @description 时长分布 */
            duration_distribution?: {
                [key: string]: number;
            };
            /** @description 热门话题 */
            trending_hashtags?: string[];
        };
        /** @description TrendInsight作者搜索响应模型 */
        TrendInsightAuthorSearchResponseSchema: {
            /** @description 作者列表 */
            authors?: components["schemas"]["TrendInsightAuthorSchema"][];
            /**
             * @description 总数量
             * @example 50
             */
            total?: number;
            /**
             * @description 当前页码
             * @example 1
             */
            page?: number;
            /**
             * @description 每页大小
             * @example 20
             */
            page_size?: number;
            /**
             * @description 是否有更多
             * @example true
             */
            has_more?: boolean;
        };
        /** @description 点赞数（支持整数或字符串格式） */
        Likes: number | string;
        /** @description 索引（支持整数或字符串格式） */
        Index: number | string;
        /** @description 视频项数据模型（基于实际VideoItem结构体） */
        VideoItem: {
            /**
             * @description 视频ID
             * @example 7517079878916885814
             */
            itemId: string;
            /**
             * @description 视频标题
             * @example #家有中华田园犬 #毛孩子的日常 旺财大清早就被小黄追着打
             */
            title: string;
            /**
             * @description 视频链接
             * @example https://www.douyin.com/video/7517079878916885814
             */
            url: string;
            /**
             * @description 视频缩略图URL
             * @example https://p6-byteindex-sign.byteimg.com/tos-cn-p-0015/...
             */
            thumbnail: string;
            likes: components["schemas"]["Likes"];
            /**
             * Format: int64
             * @description 视频时长(秒)
             * @example 36
             */
            duration: number;
            /**
             * @description 作者昵称
             * @example 怀中猫  🍀
             */
            nickname: string;
            /**
             * @description 作者头像URL
             * @example https://p6-byteindex-sign.byteimg.com/aweme-avatar/...
             */
            avatarUrl: string;
            /**
             * @description 创建时间
             * @example 2025-06-18 08:29:13
             */
            createTime: string;
            index: components["schemas"]["Index"];
            /**
             * Format: int64
             * @description 粉丝数
             * @example 3559
             */
            fans: number;
            /**
             * Format: int64
             * @description 是否低粉但热门
             * @example 0
             */
            isLowFansButHot: number;
            /**
             * Format: int64
             * @description 是否高点赞率
             * @example 1
             */
            isHighLikeRate: number;
            /**
             * Format: int64
             * @description 是否高播放完成率
             * @example 0
             */
            isHighPlayOverRate: number;
            /**
             * Format: int64
             * @description 是否高粉丝增长率
             * @example 0
             */
            isHighFansRiseRate: number;
        };
        /** @description TrendInsight视频API响应模型 */
        TrendInsightVideoResponseSchema: {
            /**
             * Format: int64
             * @description 视频记录ID
             * @example 1
             */
            id?: number;
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2024-01-15T10:30:00Z
             */
            created_at?: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2024-01-15T10:30:00Z
             */
            updated_at?: string;
            /**
             * @description 视频ID
             * @example 7517079878916885814
             */
            video_id: string;
            /**
             * @description 视频标题
             * @example #家有中华田园犬 #毛孩子的日常 旺财大清早就被小黄追着打
             */
            title?: string;
            /** @description 视频描述 */
            description?: string;
            /**
             * @description 视频链接
             * @example https://www.douyin.com/video/7517079878916885814
             */
            video_url?: string;
            /**
             * @description 封面链接
             * @example https://p6-byteindex-sign.byteimg.com/tos-cn-p-0015/...
             */
            cover_url?: string;
            /**
             * Format: int64
             * @description 视频时长(秒)
             * @example 36
             */
            duration?: number;
            /** @description 抖音ID */
            aweme_id?: string;
            /**
             * @description 用户名
             * @example 怀中猫  🍀
             */
            user_name?: string;
            /**
             * @description 作者头像
             * @example https://p6-byteindex-sign.byteimg.com/aweme-avatar/...
             */
            author_avatar?: string;
            /**
             * Format: int64
             * @description 作者粉丝数
             * @example 3559
             */
            author_followers?: number;
            /**
             * Format: int64
             * @description 播放量
             */
            play_count?: number;
            /**
             * Format: int64
             * @description 点赞数
             * @example 963762
             */
            like_count?: number;
            /**
             * Format: int64
             * @description 评论数
             */
            comment_count?: number;
            /**
             * Format: int64
             * @description 分享数
             */
            share_count?: number;
            /**
             * Format: int64
             * @description 收藏数
             */
            collect_count?: number;
            /** @description 分类 */
            category?: string;
            /** @description 分类ID */
            category_id?: string;
            /** @description 标签列表 */
            tags?: string[];
            /**
             * @description 发布时间
             * @example 2025-06-18 08:29:13
             */
            publish_time?: string;
            /**
             * @description 平台名称
             * @example douyin
             */
            platform?: string;
            /** @description 源关键词 */
            source_keyword?: string;
        };
        /** @description TrendInsight作者响应模型 */
        TrendInsightAuthorSchema: {
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2024-06-19T10:30:00Z
             */
            created_at: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2024-06-19T10:30:00Z
             */
            updated_at: string;
            /**
             * @description 用户ID
             * @example jdcdjaeighc
             */
            user_id: string;
            /**
             * @description 用户名
             * @example 郑工
             */
            user_name: string;
            /**
             * @description 用户头像URL
             * @example https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813_8fb6569579a042dfbd665507c86ca716.jpeg?from=**********
             */
            user_head_logo: string;
            /**
             * @description 作品数量（原始字符串格式）
             * @example 0
             */
            item_count: string;
            /**
             * @description 关注数（原始字符串格式）
             * @example 194
             */
            follow_count: string;
            /**
             * @description 点赞数（原始字符串格式）
             * @example 1246
             */
            like_count: string;
            /**
             * @description 作品数量（整数格式，可选）
             * @example 0
             */
            item_count_int?: number;
            /**
             * @description 关注数（整数格式，可选）
             * @example 194
             */
            follow_count_int?: number;
            /**
             * @description 点赞数（整数格式，可选）
             * @example 1246
             */
            like_count_int?: number;
            /**
             * @description 主要分类标签
             * @example 未分类（投稿数不足）
             */
            first_tag_name: string;
            /**
             * @description 次要分类标签
             * @example
             */
            second_tag_name: string;
            /**
             * @description 抖音ID
             * @example 1026822424
             */
            aweme_id: string;
            /**
             * @description 抖音主页URL
             * @example https://www.douyin.com/user/MS4wLjABAAAAml5oJboO7n1OG4BvF3UoivwWR54s2MbE732SZ7hGOmI
             */
            aweme_url: string;
            /**
             * @description 平台名称（可选）
             * @example douyin
             */
            platform?: string;
            /**
             * @description 爬取时间戳
             * @example 1718794200
             */
            crawl_time: number;
            /**
             * @description 源关键词（可选）
             * @example 科技博主
             */
            source_keyword?: string;
            /** @description 原始数据（JSON对象） */
            raw_data: {
                [key: string]: unknown;
            };
        };
        VideoKeywordStatsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取video关键词统计信息成功
             */
            msg?: string;
            data?: {
                /**
                 * @description 关键词总数
                 * @example 15
                 */
                keyword_count?: number;
                /**
                 * @description 有数据的数据源数
                 * @example 12
                 */
                active_sources?: number;
                /**
                 * @description 所有关键词的视频总数
                 * @example 15000
                 */
                total_videos?: number;
                /**
                 * @description 今日新增视频数
                 * @example 150
                 */
                today_count?: number;
                /**
                 * @description 平均每个关键词的视频数
                 * @example 1000
                 */
                avg_videos_per_keyword?: number;
                /**
                 * @description 统计时间
                 * @example 2024-01-15 10:30:00
                 */
                last_updated?: string;
            };
        };
        AuthorKeywordStatsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取author关键词统计信息成功
             */
            msg?: string;
            data?: {
                /**
                 * @description 关键词总数
                 * @example 8
                 */
                keyword_count?: number;
                /**
                 * @description 有数据的数据源数
                 * @example 6
                 */
                active_sources?: number;
                /**
                 * @description 所有关键词的作者总数
                 * @example 2400
                 */
                total_authors?: number;
                /**
                 * @description 今日新增作者数
                 * @example 30
                 */
                today_count?: number;
                /**
                 * @description 平均每个关键词的作者数
                 * @example 300
                 */
                avg_authors_per_keyword?: number;
                /**
                 * @description 统计时间
                 * @example 2024-01-15 10:30:00
                 */
                last_updated?: string;
            };
        };
        GetSyncRecordsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取同步记录成功
             */
            msg?: string;
            data?: {
                /** @description 同步记录列表 */
                records?: components["schemas"]["DouyinSyncRecord"][];
                /** @description 分页信息 */
                pagination?: {
                    /**
                     * @description 当前页码
                     * @example 1
                     */
                    page?: number;
                    /**
                     * @description 每页记录数
                     * @example 20
                     */
                    page_size?: number;
                    /**
                     * @description 总记录数
                     * @example 100
                     */
                    total?: number;
                    /**
                     * @description 总页数
                     * @example 5
                     */
                    total_page?: number;
                };
            };
        };
        GetSyncRecordRelatedListResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取同步记录关联成功
             */
            msg?: string;
            data?: {
                /** @description 关联记录列表 */
                records?: components["schemas"]["UserDouyinCollectSyncRecordRelated"][];
                /** @description 分页信息 */
                pagination?: {
                    /**
                     * @description 当前页码
                     * @example 1
                     */
                    page?: number;
                    /**
                     * @description 每页记录数
                     * @example 20
                     */
                    page_size?: number;
                    /**
                     * @description 总记录数
                     * @example 100
                     */
                    total?: number;
                    /**
                     * @description 总页数
                     * @example 5
                     */
                    total_page?: number;
                };
            };
        };
        GetSyncStatsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取同步统计成功
             */
            msg?: string;
            data?: components["schemas"]["DouyinSyncStats"];
        };
        GetUserVideoStatsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取视频统计成功
             */
            msg?: string;
            data?: components["schemas"]["UserVideoStats"];
        };
        GetSyncRecordRelatedResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取同步记录关联成功
             */
            msg?: string;
            data?: {
                /** @description 关联记录列表 */
                records?: components["schemas"]["UserDouyinCollectSyncRecordRelated"][];
                /** @description 分页信息 */
                pagination?: {
                    /**
                     * @description 当前页码
                     * @example 1
                     */
                    page?: number;
                    /**
                     * @description 每页记录数
                     * @example 20
                     */
                    page_size?: number;
                    /**
                     * @description 总记录数
                     * @example 100
                     */
                    total?: number;
                    /**
                     * @description 总页数
                     * @example 5
                     */
                    total_page?: number;
                };
                /**
                 * @description 同步记录UUID
                 * @example 1234567890abcdef1234567890abcdef
                 */
                sync_record_uuid?: string;
            };
        };
        UserDouyinCollectSyncRecordRelated: {
            /**
             * @description 关联记录ID
             * @example 1
             */
            id?: number;
            /**
             * @description 关联记录UUID
             * @example related_1234567890abcdef1234567890abcdef
             */
            uuid?: string;
            /**
             * @description 用户UUID
             * @example user_1234567890abcdef1234567890abcdef
             */
            user_uuid?: string;
            /**
             * @description 同步记录UUID
             * @example 1234567890abcdef1234567890abcdef
             */
            sync_record_uuid?: string;
            /**
             * @description 抖音视频ID
             * @example 7123456789012345678
             */
            aweme_id?: string;
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2024-01-15T10:30:00Z
             */
            create_time?: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2024-01-15T10:30:00Z
             */
            update_time?: string;
            /**
             * @description 是否已删除
             * @example false
             */
            is_deleted?: boolean;
            /** @description 关联的抖音视频详细信息，如果为null表示视频信息未找到 */
            douyin_aweme?: components["schemas"]["DouyinAweme"] | null;
        };
        SyncCollectsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 同步成功，新增 5 个视频
             */
            msg?: string;
        };
        DouyinSyncRecord: {
            /**
             * @description 记录ID
             * @example 1
             */
            id?: number;
            /**
             * @description 记录UUID
             * @example 1234567890abcdef1234567890abcdef
             */
            uuid?: string;
            /**
             * @description 用户UUID
             * @example user_1234567890abcdef1234567890abcdef
             */
            user_uuid?: string;
            /**
             * @description 同步视频数量
             * @example 5
             */
            sync_count?: number;
            /**
             * @description 同步状态
             * @example SUCCESS
             * @enum {string}
             */
            sync_status?: "SUCCESS" | "FAILED";
            /**
             * Format: date-time
             * @description 同步时间
             * @example 2024-01-15T10:30:00Z
             */
            sync_time?: string;
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2024-01-15T10:30:00Z
             */
            created_at?: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2024-01-15T10:30:00Z
             */
            updated_at?: string;
        };
        DouyinSyncStats: {
            /**
             * @description 总同步记录数
             * @example 100
             */
            total_records?: number;
            /**
             * @description 成功同步记录数
             * @example 95
             */
            success_records?: number;
            /**
             * @description 失败同步记录数
             * @example 5
             */
            failed_records?: number;
            /**
             * @description 总同步视频数
             * @example 500
             */
            total_synced_videos?: number;
            /**
             * Format: float
             * @description 同步成功率（百分比）
             * @example 95
             */
            success_rate?: number;
            /**
             * Format: float
             * @description 平均每次同步视频数
             * @example 5.26
             */
            avg_videos_per_sync?: number;
            /**
             * Format: date-time
             * @description 最后同步时间
             * @example 2024-01-15T10:30:00Z
             */
            last_sync_time?: string;
            /**
             * @description 统计周期（天数）
             * @example 30
             */
            statistics_period?: number;
        };
        /** @description 用户视频统计信息，基于 user_inbox_video_related 表统计 */
        UserVideoStats: {
            /**
             * @description 统计天数（保留兼容性参数）
             * @example 30
             */
            days: number;
            /**
             * @description 用户视频总记录数（排除软删除）
             * @example 150
             */
            total_records: number;
            /** @description 按来源类型分组的统计信息 */
            source_stats: {
                /**
                 * @description 收藏夹类型视频数量
                 * @example 80
                 */
                collect: number;
                /**
                 * @description 作者类型视频数量
                 * @example 45
                 */
                author: number;
                /**
                 * @description 关键词搜索类型视频数量
                 * @example 25
                 */
                video: number;
            };
            /** @description 每种来源类型的最新记录时间 */
            latest_times: {
                /**
                 * Format: date-time
                 * @description 收藏夹类型最新记录时间
                 * @example 2024-01-15T10:30:00Z
                 */
                collect: string | null;
                /**
                 * Format: date-time
                 * @description 作者类型最新记录时间
                 * @example 2024-01-14T15:20:00Z
                 */
                author: string | null;
                /**
                 * Format: date-time
                 * @description 关键词搜索类型最新记录时间
                 * @example 2024-01-13T09:15:00Z
                 */
                video: string | null;
            };
        };
        BindDouyinCookieRequest: {
            /**
             * @description 抖音Cookie字符串
             * @example sessionid=abc123; tt_webid=xyz789; odin_tt=def456
             */
            cookie: string;
        };
        /** @description 抖音视频详细信息 */
        DouyinAweme: {
            /**
             * @description 视频记录ID
             * @example 1
             */
            ID?: number;
            /**
             * @description 用户ID
             * @example 1691476220714126
             */
            UserID?: string;
            /**
             * @description 用户sec_uid
             * @example MS4wLjABAAAATvZSy52IJhciYEEddmA36TZ0JWzDCQv98DZick-GbYlPe1ML-PirtAwzj9IKWddd
             */
            SecUID?: string;
            /**
             * @description 用户短ID
             * @example
             */
            ShortUserID?: string;
            /**
             * @description 用户唯一ID
             * @example
             */
            UserUniqueID?: string;
            /**
             * @description 用户昵称
             * @example 胡慢慢
             */
            Nickname?: string;
            /**
             * @description 用户头像地址
             * @example https://p3-pc.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813c001_oofmmL9EAfDjuc8EZEFkjIAkAkAGQgCpgbANgA.jpeg?from=327834062
             */
            Avatar?: string;
            /**
             * @description 用户签名
             * @example
             */
            UserSignature?: string;
            /**
             * @description 评论时的IP地址
             * @example
             */
            IPLocation?: string;
            /**
             * Format: int64
             * @description 记录添加时间戳
             * @example 0
             */
            AddTs?: number;
            /**
             * Format: int64
             * @description 记录最后修改时间戳
             * @example 0
             */
            LastModifyTs?: number;
            /**
             * @description 视频ID
             * @example 7509429111140420874
             */
            AwemeID?: string;
            /**
             * @description 视频类型
             * @example 0
             */
            AwemeType?: string;
            /**
             * @description 视频标题
             * @example 开局一个碗，从董天宝的视角看太极张三丰
             */
            Title?: string;
            /**
             * @description 视频描述
             * @example 开局一个碗，从董天宝的视角看太极张三丰 #太极张三丰 #独白 #香港电影
             */
            Desc?: string;
            /**
             * Format: date-time
             * @description 视频发布时间
             * @example 2022-01-01T00:00:00Z
             */
            CreateTime?: string;
            /**
             * @description 视频点赞数
             * @example 65954
             */
            LikedCount?: string;
            /**
             * @description 视频评论数
             * @example 1832
             */
            CommentCount?: string;
            /**
             * @description 视频分享数
             * @example 3652
             */
            ShareCount?: string;
            /**
             * @description 视频收藏数
             * @example 6095
             */
            CollectedCount?: string;
            /**
             * @description 视频详情页URL
             * @example https://www.iesdouyin.com/share/video/7509429111140420874/?region=CN&mid=7509429671579241267&u_code=1j5e1iiim7il&did=MS4wLjABAAAABZRr28wVdkSMyhDKjTsWMcrU1HeWHUaU11_4gbfzUOcobe1HiRQ3vZNyzZ9UHWfi&iid=MS4wLjABAAAANwkJuWIRFOzg5uCpDRpMj4OX-QryoDgn-yYlXQnRwQQ&with_sec_did=1&video_share_track_ver=&titleType=title&share_sign=bCtk9fb69OCmzbh9PVoR7GYUhakGM90kP657bi4cgoY-&share_version=170400&ts=**********&from_aid=6383&from_ssr=1
             */
            AwemeURL?: string;
            /**
             * @description 视频封面图URL
             * @example https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/b48afb45639b441395648b5e73377dee~tplv-dy-cropcenter:323:430.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=Zkek3oPyA35me2WA6uHqKu16ojo%3D&from=327834062&s=PackSourceEnum_COLLECTION&se=true&sh=323_430&sc=cover&biz_tag=pcweb_cover&l=2025070223504777EDDC580CE694433A0D
             */
            CoverURL?: string;
            /**
             * @description 视频下载地址
             * @example https://v3-web.douyinvod.com/e9167f11bad193d88dbdf468b643c947/686581d8/video/tos/cn/tos-cn-ve-15/oMH2IALiYuPY2EBbAg4eAfUuWv1e1gCDeKZ28F/?a=6383&ch=42&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C3&cv=1&br=2304&bt=2304&cs=0&ds=4&ft=AJkeU_TLRR0s~hC32D12Nc.xBiGNbLeSYfdU_4cE9W_2Nv7TGW&mime_type=video_mp4&qs=0&rc=OWY3ODllZzM2aTg1ZjY8ZEBpam1xb3Y5cjNnMzMzNGkzM0AtYjQyNGBeXjMxMTBfLWBfYSNhMnJpMmRrZ29hLS1kLS9zcw%3D%3D&btag=c0000e00030000&cquery=101r_100B_100H_100K_100o&dy_q=**********&feature_id=46a7bb47b4fd1280f3d3825bf2b29388&l=2025070223504777EDDC580CE694433A0D
             */
            VideoDownloadURL?: string;
            /**
             * @description 搜索来源关键字
             * @example
             */
            SourceKeyword?: string;
        };
        BindDouyinCookieResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example cookie 绑定成功
             */
            msg?: string;
            /**
             * @description 绑定结果
             * @example true
             */
            data?: boolean;
        };
        GetVideoDetailResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 获取视频详情成功
             */
            msg?: string;
            /** @description 视频详情数据 */
            data?: {
                /**
                 * @description 抖音视频ID
                 * @example 7123456789012345678
                 */
                aweme_id?: string;
                /**
                 * @description 视频描述
                 * @example 分享一个有趣的科技小知识
                 */
                desc?: string;
                /**
                 * @description 视频标题
                 * @example 你知道吗？人工智能的发展历史
                 */
                title?: string;
                /**
                 * @description 作者昵称
                 * @example 科技小达人
                 */
                nickname?: string;
                /**
                 * @description 作者用户ID
                 * @example 123456789
                 */
                user_id?: string;
                /**
                 * @description 视频详情页URL
                 * @example https://www.douyin.com/video/7123456789012345678
                 */
                aweme_url?: string;
                /**
                 * @description 视频封面图URL
                 * @example https://p3.douyinpic.com/img/aweme-cover/...
                 */
                cover_url?: string;
                /**
                 * @description 视频下载地址
                 * @example https://aweme.snssdk.com/aweme/v1/play/...
                 */
                video_download_url?: string;
                /**
                 * Format: int64
                 * @description 视频发布时间戳
                 * @example **********
                 */
                create_time?: number;
                /**
                 * @description 视频点赞数
                 * @example 1.2w
                 */
                liked_count?: string;
                /**
                 * @description 视频评论数
                 * @example 856
                 */
                comment_count?: string;
                /**
                 * @description 视频分享数
                 * @example 234
                 */
                share_count?: string;
                /**
                 * @description 视频收藏数
                 * @example 567
                 */
                collected_count?: string;
                /**
                 * @description 视频时长（秒）
                 * @example 45
                 */
                duration?: number;
                /**
                 * @description 视频宽度
                 * @example 720
                 */
                width?: number;
                /**
                 * @description 视频高度
                 * @example 1280
                 */
                height?: number;
            };
        };
        VideoRelatedListResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 查询成功
             */
            message?: string;
            data?: {
                /** @description 视频关联记录列表 */
                list?: components["schemas"]["VideoRelatedRecord"][];
                /**
                 * @description 总记录数
                 * @example 150
                 */
                total?: number;
                /**
                 * @description 当前页码
                 * @example 1
                 */
                page?: number;
                /**
                 * @description 每页数量
                 * @example 20
                 */
                page_size?: number;
            };
            /**
             * @description 刷新后的token（如果需要）
             * @example refreshed_token_if_needed
             */
            token?: string;
            /**
             * @description 响应时间戳
             * @example 1672531200000
             */
            time?: number;
        };
        VideoRelatedRecord: {
            /**
             * @description 记录的唯一标识符
             * @example 184e796adbd519e0-184e796adbd519e1
             */
            uuid?: string;
            /**
             * @description 来源ID（如收藏夹ID、关键词ID等）
             * @example 7495633625980131112
             */
            source_id?: string;
            /**
             * @description 抖音视频ID
             * @example 7509429111140420874
             */
            aweme_id?: string;
            /**
             * @description 来源类型（返回大写枚举值）
             * @example COLLECT
             * @enum {string}
             */
            source_type?: "KEYWORD" | "AUTHOR" | "COLLECT";
            /**
             * @description 处理状态
             * @example PENDING
             * @enum {string}
             */
            handle_status?: "PENDING" | "SUCCESS" | "FAILED";
            /**
             * Format: date-time
             * @description 视频发布时间（ISO 8601格式）
             * @example 2025-07-02T23:40:04+08:00
             */
            publish_time?: string;
            /**
             * Format: date-time
             * @description 记录创建时间（ISO 8601格式）
             * @example 2025-07-02T23:40:04+08:00
             */
            createTime?: string;
            /**
             * Format: date-time
             * @description 记录更新时间（ISO 8601格式）
             * @example 2025-07-02T23:40:04+08:00
             */
            updateTime?: string;
            /** @description 关联的抖音视频详细信息，包含视频标题、描述、作者、统计数据等。如果为null表示视频信息未找到或跨库查询失败 */
            douyin_aweme?: components["schemas"]["DouyinAweme"] | null;
            /**
             * Format: double
             * @description 趋势评分，计算方式为 int(trendinsight_video.trend_radio * trendinsight_video.trend_score)，结果保留整数，如果不存在趋势数据则为null
             * @example 85
             */
            trend_score?: number | null;
        };
        VideoRelatedStatsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code?: number;
            /**
             * @description 响应消息
             * @example 查询成功
             */
            message?: string;
            data?: {
                /**
                 * @description 总记录数
                 * @example 150
                 */
                total?: number;
                /** @description 按来源类型分组统计 */
                source_type_stats?: {
                    /**
                     * @description 收藏夹类型数量
                     * @example 80
                     */
                    COLLECT: number;
                    /**
                     * @description 关键词类型数量
                     * @example 45
                     */
                    KEYWORD: number;
                    /**
                     * @description 作者类型数量
                     * @example 25
                     */
                    AUTHOR: number;
                };
                /** @description 各个来源类型最新一条数据的创建时间 */
                latest_times?: {
                    /**
                     * Format: date-time
                     * @description 收藏夹类型最新记录时间
                     * @example 2024-01-15T10:30:00Z
                     */
                    COLLECT: string | null;
                    /**
                     * Format: date-time
                     * @description 关键词类型最新记录时间
                     * @example 2024-01-14T15:20:00Z
                     */
                    KEYWORD: string | null;
                    /**
                     * Format: date-time
                     * @description 作者类型最新记录时间
                     * @example 2024-01-13T09:15:00Z
                     */
                    AUTHOR: string | null;
                };
            };
            /**
             * @description 刷新后的token（如果需要）
             * @example refreshed_token_if_needed
             */
            token?: string;
            /**
             * @description 响应时间戳
             * @example 1672531200000
             */
            time?: number;
        };
        AddVideoToAssetsRequest: {
            /**
             * @description 视频关联记录UUID
             * @example 1234567890abcdef1234567890abcdef
             */
            record_uuid: string;
        };
        AddVideoToAssetsResponse: {
            /**
             * @description 响应状态码
             * @example 200
             */
            code: number;
            /**
             * @description 响应消息
             * @example 视频已成功添加到素材库
             */
            msg: string;
            data: {
                /**
                 * @description 创建的素材记录UUID
                 * @example asset-uuid-456
                 */
                asset_uuid: string;
                /**
                 * @description 抖音视频ID
                 * @example 7123456789012345678
                 */
                video_id: string;
                /**
                 * @description 视频标题
                 * @example 人工智能技术发展趋势分析
                 */
                video_title: string;
                /**
                 * @description 视频作者
                 * @example 科技博主小王
                 */
                author: string;
                /**
                 * @description 素材来源类型
                 * @example COLLECTION
                 * @enum {string}
                 */
                source: "COLLECTION" | "MANUAL";
                /**
                 * @description 记录处理状态
                 * @example SUCCESS
                 * @enum {string}
                 */
                record_status: "SUCCESS";
            };
            /**
             * @description 刷新后的token（如果需要）
             * @example refreshed_token_if_needed
             */
            token?: string;
            /**
             * @description 响应时间戳
             * @example 1672531200000
             */
            time?: number;
        };
        /** @description 用户抖音收藏同步记录关联，包含视频详细信息 */
        UserDouyinCollectSyncRecordRelatedWithVideo: components["schemas"]["UserDouyinCollectSyncRecordRelated"] & {
            /** @description 关联的抖音视频信息 */
            douyin_aweme?: components["schemas"]["DouyinAweme"];
            /**
             * Format: double
             * @description 趋势评分，计算方式为 int(trendinsight_video.trend_radio * trendinsight_video.trend_score)，结果保留整数，如果不存在趋势数据则为null
             * @example 85
             */
            trend_score?: number | null;
        };
        /** @description 用户收件箱视频关联记录 */
        UserInboxVideoRelated: {
            /**
             * @description 主键UUID
             * @example 1234567890abcdef1234567890abcdef
             */
            uuid?: string;
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2025-01-03T10:30:00+08:00
             */
            create_time?: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2025-01-03T10:30:00+08:00
             */
            update_time?: string;
            /**
             * @description 是否删除
             * @example false
             */
            is_deleted?: boolean;
            /**
             * @description 用户UUID
             * @example user1234567890abcdef1234567890ab
             */
            user_uuid?: string;
            /**
             * @description 源ID（关键词ID、作者ID或收藏夹ID）
             * @example MS4wLjABAAAA1234567890abcdef
             */
            source_id?: string;
            /**
             * @description 来源类型（返回大写枚举值）
             * @example AUTHOR
             * @enum {string}
             */
            source_type?: "KEYWORD" | "AUTHOR" | "COLLECT";
            /**
             * @description 抖音视频ID
             * @example 7123456789012345678
             */
            aweme_id?: string;
            /**
             * Format: date-time
             * @description 视频发布时间
             * @example 2025-01-02T15:30:00+08:00
             */
            publish_time?: string;
            /**
             * @description 处理状态
             * @example success
             * @enum {string}
             */
            handle_status?: "pending" | "success" | "failed";
        };
        /** @description 用户收件箱视频关联记录，包含抖音视频详细信息 */
        UserInboxVideoRelatedWithAweme: components["schemas"]["UserInboxVideoRelated"] & {
            /** @description 关联的抖音视频详细信息，如果为null表示视频信息未找到或跨库查询失败 */
            douyin_aweme?: components["schemas"]["DouyinAweme"] | null;
            /**
             * Format: double
             * @description 趋势评分，计算方式为 int(trend_radio * trend_score)，结果保留整数，如果不存在趋势数据则为null
             * @example 85
             */
            trend_score?: number | null;
        };
        /** @description 趋势洞察视频信息 */
        TrendInsightVideo: {
            /**
             * @description 主键ID
             * @example 12345
             */
            id: number;
            /**
             * @description 视频ID
             * @example 7123456789012345678
             */
            video_id: string;
            /**
             * @description 视频标题
             * @example 人工智能技术发展趋势分析
             */
            title?: string;
            /**
             * @description 视频描述
             * @example 详细分析人工智能技术的最新发展趋势
             */
            description?: string;
            /**
             * @description 视频链接
             * @example https://example.com/video.mp4
             */
            video_url?: string;
            /**
             * @description 封面链接
             * @example https://example.com/cover.jpg
             */
            cover_url?: string;
            /**
             * @description 视频时长(秒)
             * @example 120
             */
            duration?: number;
            /**
             * @description 作者ID
             * @example 123456789
             */
            author_id: string;
            /**
             * @description 作者名称
             * @example 科技博主小王
             */
            author_name?: string;
            /**
             * @description 作者头像
             * @example https://example.com/avatar.jpg
             */
            author_avatar?: string;
            /**
             * @description 作者粉丝数
             * @example 100000
             */
            author_followers?: number;
            /**
             * @description 播放量
             * @example 50000
             */
            play_count?: number;
            /**
             * @description 点赞数
             * @example 1200
             */
            like_count?: number;
            /**
             * @description 评论数
             * @example 300
             */
            comment_count?: number;
            /**
             * @description 分享数
             * @example 150
             */
            share_count?: number;
            /**
             * @description 收藏数
             * @example 80
             */
            collect_count?: number;
            /**
             * @description 分类
             * @example 科技
             */
            category?: string;
            /**
             * @description 分类ID
             * @example 1
             */
            category_id?: string;
            /**
             * @description 来源平台
             * @example douyin
             */
            platform: string;
            /**
             * @description 平台原始视频ID
             * @example 7123456789012345678
             */
            platform_video_id?: string;
            /**
             * @description 发布时间戳
             * @example 1672531200
             */
            publish_time: number;
            /**
             * @description 爬取时间戳
             * @example 1672531200
             */
            crawl_time: number;
            /**
             * Format: double
             * @description 趋势评分
             * @example 85.5
             */
            trend_score: number;
            /**
             * Format: double
             * @description 趋势权重系数
             * @example 1.2
             */
            trend_radio: number;
            /**
             * Format: date-time
             * @description 创建时间
             * @example 2023-01-01T12:00:00Z
             */
            created_at?: string;
            /**
             * Format: date-time
             * @description 更新时间
             * @example 2023-01-01T12:00:00Z
             */
            updated_at?: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    searchVideos: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["VideoSearchRequest"];
            };
        };
        responses: {
            /** @description 搜索成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["VideoSearchResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 未授权访问 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 参数验证失败 */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    searchAuthors: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreatorSearchRequest"];
            };
        };
        responses: {
            /** @description 搜索成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AuthorSearchResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 未授权访问 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 参数验证失败 */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getUserVideoKeywords: {
        parameters: {
            query?: {
                /** @description 页码，从1开始 */
                page?: number;
                /** @description 每页数量 */
                page_size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetUserKeywordListResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getUserAuthorKeywords: {
        parameters: {
            query?: {
                /** @description 页码，从1开始 */
                page?: number;
                /** @description 每页数量 */
                page_size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetUserAuthorKeywordListResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    delUserKeywordParId: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 关键词关联的UUID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 删除成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DeleteUserKeywordResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 关键词不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    upsertVideoKeywords: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpsertVideoKeywordRequest"];
            };
        };
        responses: {
            /** @description 视频关键词创建/更新成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UpsertVideoKeywordResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 未授权访问 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    upsertAuthorKeywords: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpsertAuthorKeywordRequest"];
            };
        };
        responses: {
            /** @description 作者关键词创建/更新成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UpsertAuthorKeywordResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 未授权访问 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getVideoKeywordStats: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["VideoKeywordStatsResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getAuthorKeywordStats: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AuthorKeywordStatsResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getConfig: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 配置信息获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConfigResponse"];
                };
            };
            /** @description 未授权访问 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    testConnection: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 连接测试完成 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectionTestResponse"];
                };
            };
            /** @description 未授权访问 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getApiInfo: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description API信息获取成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ApiInfoResponse"];
                };
            };
            /** @description 未授权访问 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 权限不足 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getSyncRecords: {
        parameters: {
            query?: {
                /** @description 页码，从1开始 */
                page?: number;
                /** @description 每页记录数，最大100 */
                page_size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取同步记录成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetSyncRecordsResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getSyncStats: {
        parameters: {
            query?: {
                /** @description 统计天数参数（保留兼容性，当前版本基于实际记录统计） */
                days?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取视频统计成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetUserVideoStatsResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getSyncRecordRelated: {
        parameters: {
            query: {
                /** @description 同步记录UUID */
                sync_record_uuid: string;
                /** @description 页码，从1开始 */
                page?: number;
                /** @description 每页记录数，最大100 */
                page_size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取同步记录关联成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetSyncRecordRelatedResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 同步记录不存在或不属于当前用户 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getSyncRecordRelatedList: {
        parameters: {
            query?: {
                /** @description 页码 */
                page?: number;
                /** @description 每页记录数 */
                page_size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取同步记录关联成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetSyncRecordRelatedListResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getVideoDetail: {
        parameters: {
            query: {
                /** @description 抖音视频ID */
                aweme_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 获取视频详情成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetVideoDetailResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 访问被拒绝 */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 视频不存在 */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 请求过于频繁 */
            429: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    syncCollects: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 同步成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SyncCollectsResponse"];
                };
            };
            /** @description 请求参数错误或用户信息不完整 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    bindDouyinCookie: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BindDouyinCookieRequest"];
            };
        };
        responses: {
            /** @description Cookie绑定成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["BindDouyinCookieResponse"];
                };
            };
            /** @description 请求参数错误或Cookie无效 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getVideoRelatedList: {
        parameters: {
            query?: {
                /** @description 来源类型过滤，支持：KEYWORD（关键词）、AUTHOR（作者）、COLLECT（收藏夹） */
                source_type?: "KEYWORD" | "AUTHOR" | "COLLECT";
                /** @description 排序字段，支持：create_time（添加时间）、publish_time（发布时间） */
                sort_by?: "create_time" | "publish_time";
                /** @description 排序方向，支持：asc（升序）、desc（降序） */
                sort_order?: "asc" | "desc";
                /** @description 页码，从1开始 */
                page?: number;
                /** @description 每页数量，范围：1-100 */
                page_size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 查询成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["VideoRelatedListResponse"];
                };
            };
            /** @description 参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getVideoRelatedStats: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 查询成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["VideoRelatedStatsResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    addVideoToAssets: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AddVideoToAssetsRequest"];
            };
        };
        responses: {
            /** @description 添加成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AddVideoToAssetsResponse"];
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getAuthorVideos: {
        parameters: {
            query: {
                /** @description 趋势洞察用户ID，用于标识特定的抖音作者，通常以MS4wLjABAAAA开头的字符串 */
                trendinsight_user_id: string;
                /** @description 页码，从1开始，用于分页查询。当数据量较大时建议使用分页 */
                page?: number;
                /** @description 每页数量，范围1-100，控制单次返回的视频数量。建议根据实际需求调整，避免单次请求数据过多 */
                page_size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 查询成功 */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example 200 */
                        code?: number;
                        /** @example 查询作者视频成功 */
                        msg?: string;
                        data?: {
                            /** @description 用户收件箱视频关联记录列表，包含视频关联信息和抖音视频详细信息 */
                            list?: components["schemas"]["UserInboxVideoRelatedWithAweme"][];
                            /** @description 总视频数量 */
                            total?: number;
                            /** @description 当前页码 */
                            page?: number;
                            /** @description 每页数量 */
                            page_size?: number;
                            /** @description 总页数 */
                            total_pages?: number;
                        };
                    };
                };
            };
            /** @description 请求参数错误 */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 用户未登录 */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description 服务器内部错误 */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
}
