<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>同步抖音收藏夹 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
    
    /* Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">同步抖音收藏夹</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <header class="mb-6">
          <h1 class="text-2xl font-bold">同步抖音收藏夹</h1>
          <p class="text-base-content opacity-60">一键同步你的抖音收藏夹，自动分析生成选题</p>
        </header>
        
        <!-- 同步操作区和统计 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <!-- 同步操作区 -->
          <div class="lg:col-span-3">
            <div class="card bg-base-100 shadow-md h-full">
              <div class="card-body">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div class="flex-1">
                    <div class="flex items-center gap-3 mb-3">
                      <h3 class="text-xl font-bold">抖音收藏夹同步</h3>
                      <div class="badge badge-error">未配置</div>
                    </div>
                    <p class="text-base-content opacity-70 mb-3">
                      自动获取你的抖音收藏夹内容，添加至灵感收件箱
                    </p>
                    <div class="flex flex-col sm:flex-row sm:items-center gap-3 text-sm">
                      <div class="flex items-center gap-2">
                        <i class="fas fa-exclamation-circle text-error"></i>
                        <span class="opacity-70">请先配置Cookie以启用同步功能</span>
                      </div>
                      <div class="flex items-center gap-2 opacity-60">
                        <i class="fas fa-clock"></i>
                        <span>最后更新：从未更新</span>
                      </div>
                    </div>
                    <div class="mt-2" id="cookie-status-alert" style="display: none;">
                      <div class="alert alert-warning py-2">
                        <i class="fas fa-exclamation-triangle text-sm"></i>
                        <div class="text-sm">
                          <span class="font-medium">Cookie可能已失效</span>
                          <span class="opacity-80">- 请重新配置以恢复同步功能</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col sm:flex-row gap-3">
                    <button class="btn bg-black text-white hover:bg-gray-800" onclick="configureAccount()">
                      <i class="fas fa-cog mr-2"></i>配置抖音 Cookie
                    </button>
                    <button class="btn btn-outline" onclick="batchSync()" disabled>
                      <i class="fas fa-download mr-2"></i>批量同步
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 同步历史 -->
        <div class="card bg-base-100 shadow-md">
          <div class="card-body">
            <div class="flex items-center justify-between mb-6">
              <h3 class="card-title text-lg">
                <i class="fas fa-history mr-2 text-primary"></i>
                同步历史记录
              </h3>
            </div>
            <div class="overflow-x-auto">
              <table class="table table-zebra">
                <thead>
                  <tr class="bg-base-200">
                    <th class="font-semibold">同步时间</th>
                    <th class="font-semibold">新增素材</th>
                    <th class="font-semibold">同步状态</th>
                    <th class="font-semibold">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="hover">
                    <td class="font-medium">2024-01-20 14:30</td>
                    <td>
                      <div class="flex items-center gap-2">
                        <i class="fas fa-plus-circle text-success text-xs"></i>
                        <span>8个</span>
                      </div>
                    </td>
                    <td><div class="badge badge-success gap-1"><i class="fas fa-check text-xs"></i>成功</div></td>
                    <td>
                      <button class="btn btn-xs btn-outline btn-primary">查看详情</button>
                    </td>
                  </tr>
                  <tr class="hover">
                    <td class="font-medium">2024-01-19 09:15</td>
                    <td>
                      <div class="flex items-center gap-2">
                        <i class="fas fa-plus-circle text-success text-xs"></i>
                        <span>5个</span>
                      </div>
                    </td>
                    <td><div class="badge badge-success gap-1"><i class="fas fa-check text-xs"></i>成功</div></td>
                    <td>
                      <button class="btn btn-xs btn-outline btn-primary">查看详情</button>
                    </td>
                  </tr>
                  <tr class="hover">
                    <td class="font-medium">2024-01-18 16:45</td>
                    <td>
                      <div class="flex items-center gap-2">
                        <i class="fas fa-plus-circle text-success text-xs"></i>
                        <span>3个</span>
                      </div>
                    </td>
                    <td><div class="badge badge-success gap-1"><i class="fas fa-check text-xs"></i>成功</div></td>
                    <td>
                      <button class="btn btn-xs btn-outline btn-primary">查看详情</button>
                    </td>
                  </tr>
                  <tr class="hover">
                    <td class="font-medium">2024-01-17 11:20</td>
                    <td>
                      <div class="flex items-center gap-2">
                        <i class="fas fa-plus-circle text-success text-xs"></i>
                        <span>12个</span>
                      </div>
                    </td>
                    <td><div class="badge badge-success gap-1"><i class="fas fa-check text-xs"></i>成功</div></td>
                    <td>
                      <button class="btn btn-xs btn-outline btn-primary">查看详情</button>
                    </td>
                  </tr>
                  <tr class="hover">
                    <td class="font-medium">2024-01-16 08:30</td>
                    <td>
                      <div class="flex items-center gap-2 opacity-60">
                        <i class="fas fa-times-circle text-error text-xs"></i>
                        <span>0个</span>
                      </div>
                    </td>
                    <td><div class="badge badge-error gap-1"><i class="fas fa-times text-xs"></i>Cookie失效</div></td>
                    <td>
                      <button class="btn btn-xs btn-outline btn-error" onclick="configureAccount()">重新配置</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">28</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm font-medium menu-active"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Cookie配置和设置模态框 -->
  <dialog id="configure_account_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box w-full max-w-none sm:w-11/12 sm:max-w-md sm:mx-auto mx-0">
      <h3 class="text-lg font-bold mb-4 flex items-center gap-2">
        <i class="fas fa-key text-primary"></i>
        配置抖音 Cookie
      </h3>
      
      <!-- Cookie配置区域 -->
      <div class="form-control mb-4">
        <label class="label">
          <span class="label-text font-medium">抖音 Cookie 信息</span>
        </label>
        <textarea class="textarea textarea-bordered h-32 focus:textarea-primary w-full" placeholder="请粘贴抖音 Cookie"></textarea>
        <label class="label">
          <span class="label-text-alt">
            <a href="https://tech-done.feishu.cn/docx/Tvw4dF1IIoFl71xvO8mcYDSnnEb" target="_blank" class="link link-primary hover:link-hover text-xs">
              <i class="fas fa-question-circle mr-1"></i>不知道如何获取？查看教程
            </a>
          </span>
        </label>
      </div>
      
      <!-- 提示信息 -->
      <div class="bg-base-200 rounded-lg p-3 text-sm mb-6">
        <div class="flex items-start gap-2">
          <i class="fas fa-info-circle text-primary mt-0.5 flex-shrink-0"></i>
          <div class="space-y-1">
            <div>• 支持每天自动同步一次</div>
            <div>• 支持无限次手动同步</div>
          </div>
        </div>
      </div>
      
      <div class="modal-action">
        <form method="dialog" class="flex gap-3 w-full justify-end">
          <button class="btn btn-outline">取消</button>
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="saveAccountConfig()">
            <i class="fas fa-save mr-2"></i>保存
          </button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- Cookie获取教程模态框 -->
  <dialog id="cookie_help_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-w-4xl">
      <h3 class="text-xl font-bold mb-6 flex items-center gap-2">
        <i class="fas fa-graduation-cap text-primary"></i>
        Cookie 获取教程
      </h3>
      
      <div class="space-y-6">
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle"></i>
          <div>
            <div class="font-medium">重要提醒</div>
            <div class="text-sm opacity-80">请确保在抖音官网（douyin.com）登录状态下获取 Cookie，并复制完整信息</div>
          </div>
        </div>
        
        <div class="steps steps-vertical lg:steps-horizontal w-full">
          <div class="step step-primary">登录抖音</div>
          <div class="step step-primary">打开开发者工具</div>
          <div class="step step-primary">找到网络请求</div>
          <div class="step step-primary">复制Cookie</div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          <div class="space-y-4">
            <div class="card bg-base-200">
              <div class="card-body">
                <h4 class="card-title text-base flex items-center gap-2">
                  <span class="badge badge-primary">1</span>
                  登录抖音网页版
                </h4>
                <p class="text-sm opacity-80">
                  在浏览器中访问 <code class="bg-base-300 px-2 py-1 rounded text-primary font-mono">douyin.com</code> 
                  并使用你的账号登录
                </p>
              </div>
            </div>
            
            <div class="card bg-base-200">
              <div class="card-body">
                <h4 class="card-title text-base flex items-center gap-2">
                  <span class="badge badge-primary">2</span>
                  打开开发者工具
                </h4>
                <p class="text-sm opacity-80 mb-2">
                  按快捷键或右键菜单打开开发者工具：
                </p>
                <div class="flex flex-wrap gap-2">
                  <kbd class="kbd kbd-sm">F12</kbd>
                  <span class="text-xs opacity-60">或</span>
                  <kbd class="kbd kbd-sm">Ctrl</kbd>+<kbd class="kbd kbd-sm">Shift</kbd>+<kbd class="kbd kbd-sm">I</kbd>
                  <span class="text-xs opacity-60">或右键选择"检查"</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="space-y-4">
            <div class="card bg-base-200">
              <div class="card-body">
                <h4 class="card-title text-base flex items-center gap-2">
                  <span class="badge badge-primary">3</span>
                  找到网络请求
                </h4>
                <p class="text-sm opacity-80 mb-2">
                  在开发者工具中：
                </p>
                <ul class="text-sm opacity-80 space-y-1">
                  <li>• 点击 <strong>Network</strong>（网络）标签页</li>
                  <li>• 刷新页面（F5）</li>
                  <li>• 选择任意一个请求</li>
                </ul>
              </div>
            </div>
            
            <div class="card bg-base-200">
              <div class="card-body">
                <h4 class="card-title text-base flex items-center gap-2">
                  <span class="badge badge-primary">4</span>
                  复制 Cookie 信息
                </h4>
                <p class="text-sm opacity-80 mb-2">
                  在请求详情中：
                </p>
                <ul class="text-sm opacity-80 space-y-1">
                  <li>• 找到 <strong>Headers</strong>（请求头）</li>
                  <li>• 找到 <strong>Cookie</strong> 字段</li>
                  <li>• 复制完整的 Cookie 值</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div class="alert alert-success">
          <i class="fas fa-lightbulb"></i>
          <div>
            <div class="font-medium">小贴士</div>
            <div class="text-sm opacity-80">Cookie 通常很长，请确保复制完整。如果同步失败，可能是 Cookie 过期，请重新获取</div>
          </div>
        </div>
      </div>
      
      <div class="modal-action mt-6">
        <form method="dialog">
          <button class="btn btn-primary">
            <i class="fas fa-check mr-2"></i>我知道了
          </button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 批量同步说明模态框 -->
  <dialog id="batch_sync_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4 flex items-center gap-2">
        <i class="fas fa-info-circle text-primary"></i>
        批量同步说明
      </h3>
      <div class="space-y-4">
        <div class="alert alert-info">
          <i class="fas fa-lightbulb"></i>
          <div>
            <div class="font-medium">同步范围</div>
            <div class="text-sm opacity-80">系统将只同步收藏夹名称中带有"素材"字样的收藏夹</div>
          </div>
        </div>
        <p class="text-sm opacity-80">
          例如：<span class="font-medium">短视频素材</span>、<span class="font-medium">创作素材</span>、<span class="font-medium">素材库</span> 等收藏夹将被同步。
        </p>
        <p class="text-sm opacity-80">
          建议您将需要同步的内容整理到名称包含"素材"的收藏夹中，以便系统智能识别并添加至灵感收件箱。
        </p>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn btn-outline">取消</button>
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="confirmBatchSync()">
            <i class="fas fa-download mr-2"></i>确认同步
          </button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 同步详情模态框 -->
  <dialog id="sync_details_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-w-3xl">
      <h3 class="text-xl font-bold mb-4 flex items-center gap-2">
        <i class="fas fa-list-ul text-primary"></i>
        <span id="sync-details-title">同步详情</span>
      </h3>
      
      <div class="divider text-sm opacity-70">新增素材列表</div>
      
      <div class="overflow-x-auto max-h-96 overflow-y-auto">
        <table class="table table-sm table-zebra">
          <thead class="bg-base-200 sticky top-0">
            <tr>
              <th class="w-16">#</th>
              <th>标题</th>
              <th>作者</th>
              <th>发布时间</th>
            </tr>
          </thead>
          <tbody id="sync-details-list">
            <!-- 动态生成的同步详情列表 -->
          </tbody>
        </table>
      </div>
      
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <script>
    // 页面状态
    let isConfigured = false;
    let cookieExpired = false;
    
    // 配置账号
    function configureAccount() {
      document.getElementById('configure_account_modal').showModal();
    }
    
    // 批量同步
    function batchSync() {
      if (!isConfigured) {
        showToast('请先配置抖音Cookie', 'warning');
        return;
      }
      
      if (cookieExpired) {
        showToast('Cookie已失效，请重新配置', 'error');
        configureAccount();
        return;
      }
      
      // 显示批量同步说明Modal
      document.getElementById('batch_sync_modal').showModal();
    }
    
    // 确认批量同步
    function confirmBatchSync() {
      showToast('开始批量同步收藏夹，请稍候...', 'info');
      
      // 模拟批量同步过程，可能遇到Cookie失效
      setTimeout(() => {
        // 模拟30%概率Cookie失效
        if (Math.random() < 0.3) {
          cookieExpired = true;
          showToast('同步失败：Cookie已失效，请重新配置', 'error');
          updatePageState();
          addFailedSyncRecord();
        } else {
          showToast('批量同步完成！新增15个素材', 'success');
          updateSyncData(50, 15);
        }
      }, 5000);
    }
    
    // 保存账号配置
    function saveAccountConfig() {
      const cookieInfo = document.querySelector('#configure_account_modal textarea').value.trim();
      
      if (!cookieInfo) {
        showToast('请填写完整的Cookie信息', 'warning');
        return;
      }
      
      // 模拟配置过程
      showToast('正在验证Cookie，请稍候...', 'info');
      
      setTimeout(() => {
        showToast('Cookie配置成功！', 'success');
        isConfigured = true;
        cookieExpired = false; // 重新配置后重置失效状态
        updatePageState();
        document.getElementById('configure_account_modal').close();
      }, 2000);
    }
    
    // 显示Cookie帮助
    function showCookieHelp() {
      document.getElementById('cookie_help_modal').showModal();
    }
    
    // 更新页面状态
    function updatePageState() {
      const statusBadge = document.querySelector('.card-body .badge');
      const statusTextContainer = document.querySelector('.flex.items-center.gap-2');
      const lastUpdateContainer = statusTextContainer.parentElement.nextElementSibling;
      const cookieAlert = document.getElementById('cookie-status-alert');
      const batchSyncBtn = document.querySelector('.btn-outline');
      const configBtn = document.querySelector('.btn.bg-black');
      
      if (isConfigured && !cookieExpired) {
        // 已配置且有效状态
        statusBadge.className = 'badge badge-success';
        statusBadge.textContent = '已配置';
        statusTextContainer.innerHTML = '<i class="fas fa-check-circle text-success"></i><span class="opacity-70">Cookie 配置正常，同步功能已启用</span>';
        lastUpdateContainer.querySelector('span').textContent = '最后更新：刚刚';
        cookieAlert.style.display = 'none';
        
        // 更新按钮
        batchSyncBtn.disabled = false;
        configBtn.innerHTML = '<i class="fas fa-sync mr-2"></i>立即同步';
        configBtn.onclick = startSync;
      } else if (isConfigured && cookieExpired) {
        // 已配置但失效状态
        statusBadge.className = 'badge badge-warning';
        statusBadge.textContent = 'Cookie失效';
        statusTextContainer.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i><span class="opacity-70">Cookie已失效，同步功能暂停</span>';
        cookieAlert.style.display = 'block';
        
        // 更新按钮
        batchSyncBtn.disabled = true;
        configBtn.innerHTML = '<i class="fas fa-redo mr-2"></i>重新配置';
        configBtn.onclick = configureAccount;
      } else {
        // 未配置状态
        statusBadge.className = 'badge badge-error';
        statusBadge.textContent = '未配置';
        statusTextContainer.innerHTML = '<i class="fas fa-exclamation-circle text-error"></i><span class="opacity-70">请先配置Cookie以启用同步功能</span>';
        lastUpdateContainer.querySelector('span').textContent = '最后更新：从未更新';
        cookieAlert.style.display = 'none';
        
        // 更新按钮
        batchSyncBtn.disabled = true;
        configBtn.innerHTML = '<i class="fas fa-cog mr-2"></i>配置抖音 Cookie';
        configBtn.onclick = configureAccount;
      }
    }
    
    // 开始同步
    function startSync() {
      if (!isConfigured) {
        showToast('请先配置抖音Cookie', 'warning');
        return;
      }
      
      if (cookieExpired) {
        showToast('Cookie已失效，请重新配置', 'error');
        configureAccount();
        return;
      }
      
      showToast('开始同步收藏夹，请稍候...', 'info');
      
      // 模拟同步过程，可能遇到Cookie失效
      setTimeout(() => {
        // 模拟20%概率Cookie失效
        if (Math.random() < 0.2) {
          cookieExpired = true;
          showToast('同步失败：Cookie已失效，请重新配置', 'error');
          updatePageState();
          addFailedSyncRecord();
        } else {
          showToast('同步完成！新增3个素材', 'success');
          updateSyncData(15, 3);
        }
      }, 3000);
    }
    
    // 更新同步数据
    function updateSyncData(syncCount = 15, materialCount = 3) {
      // 更新状态和最后同步时间
      const statusTextContainer = document.querySelector('.flex.items-center.gap-2');
      const lastUpdateContainer = statusTextContainer.parentElement.nextElementSibling;
      
      statusTextContainer.innerHTML = '<i class="fas fa-check-circle text-success"></i><span class="opacity-70">Cookie 配置正常，同步功能已启用</span>';
      lastUpdateContainer.querySelector('span').textContent = '最后更新：刚刚';
      
      // 更新同步历史
      updateSyncHistory(syncCount, materialCount);
    }
    
    // 更新同步历史
    function updateSyncHistory(syncCount, materialCount) {
      const tbody = document.querySelector('tbody');
      const now = new Date();
      const timeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      // 在表格顶部插入新记录
      const newRow = `
        <tr class="hover">
          <td class="font-medium">${timeStr}</td>
          <td>
            <div class="flex items-center gap-2">
              <i class="fas fa-plus-circle text-success text-xs"></i>
              <span>${materialCount}个</span>
            </div>
          </td>
          <td><div class="badge badge-success gap-1"><i class="fas fa-check text-xs"></i>成功</div></td>
          <td>
            <button class="btn btn-xs btn-outline btn-primary">查看详情</button>
          </td>
        </tr>
      `;
      
      tbody.insertAdjacentHTML('afterbegin', newRow);
      
      // 创建新的同步详情数据（模拟）
      syncDetailsData[timeStr] = {
        status: 'success',
        count: materialCount,
        items: Array.from({length: materialCount}, (_, i) => ({
          id: i + 1,
          title: `新同步素材 ${i + 1}`,
          author: `创作者${Math.floor(Math.random() * 100)}`,
          publishTime: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24) + 10).padStart(2, '0')}:${String(Math.floor(Math.random() * 60) + 30).padStart(2, '0')}`
        }))
      };
      
      // 更新详情按钮事件
      updateDetailButtons();
    }
    
    // 添加失败的同步记录
    function addFailedSyncRecord() {
      const tbody = document.querySelector('tbody');
      const now = new Date();
      const timeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      // 在表格顶部插入失败记录
      const newRow = `
        <tr class="hover">
          <td class="font-medium">${timeStr}</td>
          <td>
            <div class="flex items-center gap-2 opacity-60">
              <i class="fas fa-times-circle text-error text-xs"></i>
              <span>0个</span>
            </div>
          </td>
          <td><div class="badge badge-error gap-1"><i class="fas fa-times text-xs"></i>Cookie失效</div></td>
          <td>
            <button class="btn btn-xs btn-outline btn-error" onclick="configureAccount()">重新配置</button>
          </td>
        </tr>
      `;
      
      tbody.insertAdjacentHTML('afterbegin', newRow);
    }
    
    // 显示Toast提示
    function showToast(message, type = 'success') {
      const toastConfigs = {
        success: {
          alertClass: 'alert-success',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>`
        },
        info: {
          alertClass: 'alert-info',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="h-6 w-6 shrink-0 stroke-current">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
          </svg>`
        },
        warning: {
          alertClass: 'alert-warning',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>`
        },
        error: {
          alertClass: 'alert-error',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>`
        }
      };
      
      const config = toastConfigs[type] || toastConfigs.success;
      
      const toastContainer = document.createElement('div');
      toastContainer.className = 'toast toast-top toast-end';
      toastContainer.innerHTML = `
        <div role="alert" class="alert ${config.alertClass}">
          ${config.icon}
          <span>${message}</span>
        </div>
      `;
      
      document.body.appendChild(toastContainer);
      
      setTimeout(() => {
        toastContainer.style.opacity = '1';
      }, 10);
      
      setTimeout(() => {
        toastContainer.style.opacity = '0';
        setTimeout(() => {
          if (toastContainer.parentNode) {
            document.body.removeChild(toastContainer);
          }
        }, 300);
      }, 3000);
    }
    
    // 同步详情数据（模拟数据）
    const syncDetailsData = {
      '2024-01-20 14:30': {
        status: 'success',
        count: 8,
        items: [
          { id: 1, title: '如何拍摄高质量Vlog | 器材推荐和拍摄技巧', author: '摄影达人', publishTime: '2024-01-18 14:20' },
          { id: 2, title: '10个简单易学的剪辑转场技巧', author: '剪辑学院', publishTime: '2024-01-17 16:30' },
          { id: 3, title: '夏日小清新风格照片调色教程', author: 'PS大师', publishTime: '2024-01-15 10:45' },
          { id: 4, title: '创意手机摄影：用手机拍出大片感', author: '手机摄影', publishTime: '2024-01-12 20:15' },
          { id: 5, title: '超火的慢动作拍摄技巧和后期处理', author: '视频特效', publishTime: '2024-01-10 09:30' },
          { id: 6, title: '如何让Vlog画面更加电影感', author: '电影质感', publishTime: '2024-01-08 18:45' },
          { id: 7, title: '5分钟学会抖音爆款短视频构思', author: '短视频学院', publishTime: '2024-01-05 12:20' },
          { id: 8, title: '2024年最受欢迎的5种视频剪辑风格', author: '剪辑趋势', publishTime: '2024-01-01 15:10' }
        ]
      },
      '2024-01-19 09:15': {
        status: 'success',
        count: 5,
        items: [
          { id: 1, title: '美食短视频拍摄指南：5个实用技巧', author: '美食拍摄', publishTime: '2024-01-18 11:30' },
          { id: 2, title: '人像摄影中常见的光线运用', author: '摄影学院', publishTime: '2024-01-17 14:25' },
          { id: 3, title: '如何制作精美的视频片头', author: '后期剪辑', publishTime: '2024-01-15 19:40' },
          { id: 4, title: '视频调色基础：从入门到精通', author: '调色大师', publishTime: '2024-01-14 13:15' },
          { id: 5, title: '15秒广告创意文案写作技巧', author: '文案高手', publishTime: '2024-01-12 16:50' }
        ]
      },
      '2024-01-18 16:45': {
        status: 'success',
        count: 3,
        items: [
          { id: 1, title: '室内人像拍摄光线布置', author: '室内摄影', publishTime: '2024-01-17 10:20' },
          { id: 2, title: '短视频创作中的音乐选择与应用', author: '音乐剪辑', publishTime: '2024-01-16 15:35' },
          { id: 3, title: '如何制作吸引人的视频缩略图', author: '封面设计', publishTime: '2024-01-15 08:45' }
        ]
      },
      '2024-01-17 11:20': {
        status: 'success',
        count: 12,
        items: [
          { id: 1, title: '手机拍摄技巧：日常vlog实用指南', author: '手机摄影', publishTime: '2024-01-16 12:40' },
          { id: 2, title: '视频剪辑中的节奏控制技巧', author: '剪辑节奏', publishTime: '2024-01-15 17:25' },
          { id: 3, title: '旅行Vlog创作全流程', author: '旅行创作', publishTime: '2024-01-14 09:15' },
          { id: 4, title: '产品短视频拍摄指南', author: '产品展示', publishTime: '2024-01-13 14:30' },
          { id: 5, title: '视频特效制作入门教程', author: '特效制作', publishTime: '2024-01-12 19:45' },
          { id: 6, title: '如何提高短视频留存率', author: '数据分析', publishTime: '2024-01-11 11:20' },
          { id: 7, title: '2024年短视频创作趋势分析', author: '内容趋势', publishTime: '2024-01-10 16:10' },
          { id: 8, title: '视频配乐选择与剪辑技巧', author: '音乐剪辑', publishTime: '2024-01-09 13:50' },
          { id: 9, title: '如何制作引人入胜的开场白', author: '开场技巧', publishTime: '2024-01-08 18:30' },
          { id: 10, title: '视频叙事结构设计方法', author: '故事结构', publishTime: '2024-01-07 10:45' },
          { id: 11, title: '如何让你的视频更有创意', author: '创意制作', publishTime: '2024-01-06 15:20' },
          { id: 12, title: '短视频创作者的设备选购指南', author: '器材选择', publishTime: '2024-01-05 20:15' }
        ]
      }
    };
    
    // 显示同步详情
    function showSyncDetails(time) {
      const details = syncDetailsData[time];
      if (!details) {
        showToast('未找到相关同步详情', 'error');
        return;
      }
      
      // 更新模态框标题
      document.getElementById('sync-details-title').textContent = `${time} 同步详情`;
      
      // 更新素材列表
      const listContainer = document.getElementById('sync-details-list');
      listContainer.innerHTML = '';
      
      details.items.forEach((item, index) => {
        const row = document.createElement('tr');
        row.className = 'hover';
        row.innerHTML = `
          <td>${index + 1}</td>
          <td class="font-medium">${item.title}</td>
          <td>${item.author}</td>
          <td>${item.publishTime}</td>
        `;
        listContainer.appendChild(row);
      });
      
      // 显示模态框
      document.getElementById('sync_details_modal').showModal();
    }
    
    // 更新表格中查看详情按钮的事件处理
    function updateDetailButtons() {
      // 为所有"查看详情"按钮添加点击事件
      const detailButtons = document.querySelectorAll('tbody .btn-primary');
      detailButtons.forEach(button => {
        const row = button.closest('tr');
        const timeCell = row.querySelector('td:first-child');
        if (timeCell) {
          const time = timeCell.textContent.trim();
          button.onclick = () => showSyncDetails(time);
        }
      });
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      updateDetailButtons();
    });
  </script>
</body>
</html>