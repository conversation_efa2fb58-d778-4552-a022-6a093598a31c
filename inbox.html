<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>灵感收件箱 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
    
    /* Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
    
    /* 灵感卡片样式 */
    .inspiration-card {
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      transition: all 0.2s ease;
      cursor: pointer;
      min-width: 350px; /* 与网格minmax保持一致 */
    }
    
    .inspiration-card:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      border-color: #d1d5db;
      transform: translateY(-1px);
    }
    
    /* 播放按钮覆盖层 - 与素材库保持一致 */
    .inspiration-item {
      position: relative;
      overflow: hidden;
      border-radius: 0.375rem;
    }
    
    .inspiration-item::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0;
      transition: opacity 0.2s ease;
      border-radius: 0.375rem;
      z-index: 2;
    }
    
    .inspiration-item:hover::after {
      opacity: 1;
    }
    
    .inspiration-item::before {
      content: '\f04b';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      z-index: 3;
      opacity: 0;
      transition: opacity 0.2s ease;
      font-size: 1.5rem;
      pointer-events: none;
    }
    
    .inspiration-item:hover::before {
      opacity: 1;
    }
    
    /* 爆款指数样式 */
    .viral-index {
      position: absolute;
      top: 0.5rem;
      left: 0.5rem;
      z-index: 10;
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      font-size: 0.7rem;
      font-weight: 500;
      padding: 0.25rem 0.5rem;
      border-radius: 0.5rem;
      backdrop-filter: blur(4px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      opacity: 0.9;
    }
    
    /* 4:3宽高比的视频缩略图 */
    .video-thumbnail {
      width: 100%;
      aspect-ratio: 4/3;
      object-fit: cover;
      border-radius: 0.375rem;
      transition: all 0.2s ease;
    }
    
    /* 播放按钮覆盖层 */
    .play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.2s ease;
      border-radius: 0.375rem;
    }
    
    .inspiration-item:hover .play-overlay {
      opacity: 1;
    }
    
    .play-icon {
      width: 3rem;
      height: 3rem;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      font-size: 1.2rem;
      transition: all 0.2s ease;
    }
    
    .play-icon:hover {
      background-color: white;
      transform: scale(1.1);
    }
    
    /* 视频标题样式 */
    .video-title {
      font-weight: 500;
      font-size: 0.875rem;
      line-height: 1.4;
      min-height: 1.4em;
      max-height: 2.8em;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 0.5rem;
      transition: all 0.2s ease;
      cursor: pointer;
    }
    
    .video-title:hover {
      text-decoration: underline;
    }
    
    /* 作者昵称样式 */
    .author-name {
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .author-name:hover {
      text-decoration: underline;
    }
    
    /* 导入按钮样式 */
    .import-btn {
      transition: all 0.2s ease;
    }
    
    .import-btn.imported {
      background-color: #10b981 !important;
      color: white !important;
      border-color: #10b981 !important;
    }
    
    .import-btn.imported:hover {
      background-color: #059669 !important;
      border-color: #059669 !important;
    }
    
    /* 统计数据样式优化 */
    .stats-item {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.75rem;
      color: rgba(0, 0, 0, 0.6);
    }
    
    .stats-item i {
      font-size: 0.7rem;
    }
    
    /* 排序按钮样式 */
    .btn-active {
      background-color: #333333 !important;
      color: white !important;
      border-color: #333333 !important;
    }
    
    .btn-active:hover {
      background-color: #1f2937 !important;
      border-color: #1f2937 !important;
    }
    
    /* 小尺寸排序按钮样式 */
    .btn-xs.btn-active {
      background-color: #333333 !important;
      color: white !important;
      border-color: #333333 !important;
    }
    
    .btn-xs.btn-active:hover {
      background-color: #1f2937 !important;
      border-color: #1f2937 !important;
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">灵感收件箱</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <header class="mb-6 hidden lg:block">
          <h1 class="text-2xl font-bold">灵感收件箱</h1>
          <p class="text-base-content opacity-60">汇聚所有灵感来源，发现创作素材</p>
        </header>
        
        <!-- 操作区域 -->
        <!-- 操作区域已简化，排序功能移至卡片内 -->
        
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8 hidden lg:grid">
          <div class="stat bg-base-100 shadow rounded-box min-w-0 overflow-hidden">
            <div class="stat-figure text-primary flex-shrink-0">
              <i class="fas fa-inbox text-lg sm:text-xl md:text-2xl"></i>
            </div>
            <div class="stat-title text-sm truncate">收件箱</div>
            <div class="stat-value text-primary text-xl sm:text-2xl md:text-3xl">20</div>
            <div class="stat-desc text-xs truncate">总视频数量</div>
          </div>
          
          <div class="stat bg-base-100 shadow rounded-box min-w-0 overflow-hidden">
            <div class="stat-figure text-secondary flex-shrink-0">
              <i class="fas fa-star text-lg sm:text-xl md:text-2xl"></i>
            </div>
            <div class="stat-title text-sm truncate">收藏夹</div>
            <div class="stat-value text-secondary text-xl sm:text-2xl md:text-3xl">15</div>
            <div class="stat-desc text-xs truncate">最后更新时间：2024-01-20 12:15</div>
          </div>
          
          <div class="stat bg-base-100 shadow rounded-box min-w-0 overflow-hidden">
            <div class="stat-figure text-info flex-shrink-0">
              <i class="fas fa-eye text-lg sm:text-xl md:text-2xl"></i>
            </div>
            <div class="stat-title text-sm truncate">监控账号</div>
            <div class="stat-value text-info text-xl sm:text-2xl md:text-3xl">5</div>
            <div class="stat-desc text-xs truncate">最后更新时间：2024-01-20 14:30</div>
          </div>
          
          <div class="stat bg-base-100 shadow rounded-box min-w-0 overflow-hidden">
            <div class="stat-figure text-accent flex-shrink-0">
              <i class="fas fa-search text-lg sm:text-xl md:text-2xl"></i>
            </div>
            <div class="stat-title text-sm truncate">监控关键词</div>
            <div class="stat-value text-accent text-xl sm:text-2xl md:text-3xl">8</div>
            <div class="stat-desc text-xs truncate">最后更新时间：2024-01-20 13:45</div>
          </div>
        </div>
        
        <!-- 灵感内容列表 -->
        <div class="card bg-base-100 shadow-md">
          <div class="card-body">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
              <h3 class="card-title text-lg sm:text-xl">最新灵感</h3>
              <div class="flex flex-col sm:flex-row gap-2 items-start sm:items-center w-full sm:w-auto">
              <div class="flex flex-wrap gap-2">
                <button id="filter-all" class="btn btn-xs btn-outline btn-active" onclick="filterByType('all')">全部</button>
                <button id="filter-favorite" class="btn btn-xs btn-ghost" onclick="filterByType('favorite')">收藏夹</button>
                <button id="filter-account" class="btn btn-xs btn-ghost" onclick="filterByType('account')">监控账号</button>
                <button id="filter-keyword" class="btn btn-xs btn-ghost" onclick="filterByType('keyword')">监控关键词</button>
                </div>
                <div class="divider divider-horizontal mx-2 hidden sm:flex"></div>
                <div class="flex flex-wrap gap-2">
                  <button id="sort-by-add-time" class="btn btn-xs btn-outline btn-active" onclick="sortByAddTime()">
                    <i class="fas fa-clock mr-1"></i><span class="hidden sm:inline">按添加时间</span><span class="sm:hidden">添加时间</span>
                  </button>
                  <button id="sort-by-publish-time" class="btn btn-xs btn-outline" onclick="sortByPublishTime()">
                    <i class="fas fa-calendar mr-1"></i><span class="hidden sm:inline">按发布时间</span><span class="sm:hidden">发布时间</span>
                  </button>
                </div>
              </div>
            </div>
            
            <div class="grid gap-4" id="inspiration-grid" style="grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); max-width: 100%;">
              <!-- 灵感项目1 -->
              <div class="inspiration-card bg-base-100 p-3 flex flex-col" data-add-time="2024-01-20T14:30:00" data-publish-time="2024-01-19T18:30:00" data-type="favorite" data-imported="false">
                <div class="inspiration-item mb-3" onclick="openVideo('https://www.douyin.com/video/7506096566928674088')">
                  <div class="viral-index">当前热度指数 8.2万</div>
                  <img src="https://p3-pc-sign.douyinpic.com/image-cut-tos/b6926cbf79f7e532794658af08ebe7b3~tplv-dy-vqe2-sr-opt1:640:480:q80.webp?from=**********&x-expires=**********&x-signature=iTIBAHbMT2fOBU3Qy7X5mkdEn1c%3D" alt="视频缩略图" class="video-thumbnail">
                </div>
                <div class="flex-1 flex flex-col">
                  <h4 class="video-title mb-2" onclick="openVideo('https://www.douyin.com/video/7506096566928674088')">新房装修完，怎么除甲醛最有效？</h4>
                  <div class="flex items-center justify-between text-xs opacity-60 mb-3">
                    <span class="author-name" onclick="openAuthorPage('@装修达人'); event.stopPropagation();">@装修达人</span>
                    <div class="flex items-center gap-1">
                      <span class="time-add">2小时前添加</span>
                      <span class="time-publish" style="display:none">1天前发布</span>
                      <span>·</span>
                      <span>收藏夹</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs mt-auto">
                    <div class="flex gap-3">
                      <span class="stats-item"><i class="fas fa-heart"></i>3.6万</span>
                      <span class="stats-item"><i class="fas fa-comment"></i>562</span>
                      <span class="stats-item"><i class="fas fa-star"></i>1.2万</span>
                      <span class="stats-item"><i class="fas fa-share"></i>890</span>
                    </div>
                    <button class="btn btn-xs btn-outline import-btn" onclick="showImportModal(this, '新房装修完，怎么除甲醛最有效？'); event.stopPropagation();">导入素材库</button>
                  </div>
                </div>
              </div>
              
              <!-- 灵感项目2 -->
              <div class="inspiration-card bg-base-100 p-3 flex flex-col" data-add-time="2024-01-20T10:15:00" data-publish-time="2024-01-20T09:15:00" data-type="account" data-imported="true">
                <div class="inspiration-item mb-3" onclick="openVideo('https://www.douyin.com/video/7506096566928674089')">
                  <div class="viral-index">尚未更新热度指数</div>
                  <img src="https://p3-pc-sign.douyinpic.com/image-cut-tos/0f6ff4e99d6540d28dff83cedb0d51c6~tplv-dy-vqe2-sr-opt1:640:480:q80.webp?from=**********&x-expires=**********&x-signature=BqtYbdVEEYOAZuqX1ZU7iXl9VB8%3D" alt="视频缩略图" class="video-thumbnail">
                </div>
                <div class="flex-1 flex flex-col">
                  <h4 class="video-title mb-2" onclick="openVideo('https://www.douyin.com/video/7506096566928674089')">效率提升技巧：每天多出2小时，让你的工作和生活更加高效，不再焦虑时间不够用</h4>
                  <div class="flex items-center justify-between text-xs opacity-60 mb-3">
                    <span class="author-name" onclick="openAuthorPage('@效率专家'); event.stopPropagation();">@效率专家</span>
                    <div class="flex items-center gap-1">
                      <span class="time-add">4小时前添加</span>
                      <span class="time-publish" style="display:none">5小时前发布</span>
                      <span>·</span>
                      <span>监控账号</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs mt-auto">
                    <div class="flex gap-3">
                      <span class="stats-item"><i class="fas fa-heart"></i>2.1万</span>
                      <span class="stats-item"><i class="fas fa-comment"></i>324</span>
                      <span class="stats-item"><i class="fas fa-star"></i>8.5千</span>
                      <span class="stats-item"><i class="fas fa-share"></i>456</span>
                    </div>
                    <button class="btn btn-xs btn-outline import-btn imported" onclick="event.stopPropagation();">已导入</button>
                  </div>
                </div>
              </div>
              
              <!-- 灵感项目3 -->
              <div class="inspiration-card bg-base-100 p-3 flex flex-col" data-add-time="2024-01-20T08:45:00" data-publish-time="2024-01-20T08:30:00" data-type="account" data-imported="false">
                <div class="inspiration-item mb-3" onclick="openVideo('https://www.douyin.com/video/7506096566928674090')">
                  <div class="viral-index">当前热度指数 756</div>
                  <img src="https://p3-pc-sign.douyinpic.com/image-cut-tos/5e37bd3e595f3e243234f95193535daa~tplv-dy-vqe2-sr-opt1:640:480:q80.webp?from=**********&x-expires=**********&x-signature=hhXbBbMPYPjdQAsKtqD4bIiMxHE%3D" alt="视频缩略图" class="video-thumbnail">
                </div>
                <div class="flex-1 flex flex-col">
                  <h4 class="video-title mb-2" onclick="openVideo('https://www.douyin.com/video/7506096566928674090')">手机摄影技巧</h4>
                  <div class="flex items-center justify-between text-xs opacity-60 mb-3">
                    <span class="author-name" onclick="openAuthorPage('@摄影小技巧'); event.stopPropagation();">@摄影小技巧</span>
                    <div class="flex items-center gap-1">
                      <span class="time-add">6小时前添加</span>
                      <span class="time-publish" style="display:none">6小时前发布</span>
                      <span>·</span>
                      <span>监控账号</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs mt-auto">
                    <div class="flex gap-3">
                      <span class="stats-item"><i class="fas fa-heart"></i>5.2万</span>
                      <span class="stats-item"><i class="fas fa-comment"></i>891</span>
                      <span class="stats-item"><i class="fas fa-star"></i>2.3万</span>
                      <span class="stats-item"><i class="fas fa-share"></i>1.1千</span>
                    </div>
                    <button class="btn btn-xs btn-outline import-btn" onclick="showImportModal(this, '手机摄影技巧'); event.stopPropagation();">导入素材库</button>
                  </div>
                </div>
              </div>
              
              <!-- 灵感项目4 -->
              <div class="inspiration-card bg-base-100 p-3 flex flex-col" data-add-time="2024-01-20T06:30:00" data-publish-time="2024-01-20T06:15:00" data-type="favorite" data-imported="false">
                <div class="inspiration-item mb-3" onclick="openVideo('https://www.douyin.com/video/7506096566928674091')">
                  <div class="viral-index">当前热度指数 0</div>
                  <img src="https://p3-pc-sign.douyinpic.com/image-cut-tos/312aaa0b436efff264adec33d97b55ae~tplv-dy-vqe2-sr-opt1:640:480:q80.webp?from=**********&x-expires=**********&x-signature=Rgc9UMjlI3HYvxgtMRkoeZBLuvs%3D" alt="视频缩略图" class="video-thumbnail">
                </div>
                <div class="flex-1 flex flex-col">
                  <h4 class="video-title mb-2" onclick="openVideo('https://www.douyin.com/video/7506096566928674091')">10分钟家常菜，简单又下饭，让你轻松解决一日三餐，再也不用为做饭发愁</h4>
                  <div class="flex items-center justify-between text-xs opacity-60 mb-3">
                    <span class="author-name" onclick="openAuthorPage('@美食制作'); event.stopPropagation();">@美食制作</span>
                    <div class="flex items-center gap-1">
                      <span class="time-add">8小时前添加</span>
                      <span class="time-publish" style="display:none">8小时前发布</span>
                      <span>·</span>
                      <span>收藏夹</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs mt-auto">
                    <div class="flex gap-3">
                      <span class="stats-item"><i class="fas fa-heart"></i>1.8万</span>
                      <span class="stats-item"><i class="fas fa-comment"></i>267</span>
                      <span class="stats-item"><i class="fas fa-star"></i>6.7千</span>
                      <span class="stats-item"><i class="fas fa-share"></i>334</span>
                    </div>
                    <button class="btn btn-xs btn-outline import-btn" onclick="showImportModal(this, '10分钟家常菜，简单又下饭'); event.stopPropagation();">导入素材库</button>
                  </div>
                </div>
              </div>
              
              <!-- 灵感项目5 -->
              <div class="inspiration-card bg-base-100 p-3 flex flex-col" data-add-time="2024-01-20T05:15:00" data-publish-time="2024-01-20T04:30:00" data-type="keyword" data-imported="true">
                <div class="inspiration-item mb-3" onclick="openVideo('https://www.douyin.com/video/7506096566928674092')">
                  <div class="viral-index">当前热度指数 156万</div>
                  <img src="https://p3-pc-sign.douyinpic.com/image-cut-tos/11325d2b4046243b17777b6c932887f1~tplv-dy-vqe2-sr-opt1:640:480:q80.webp?from=**********&x-expires=**********&x-signature=RZnSOFoHqGanIgz8PFs8uV8mTL0%3D" alt="视频缩略图" class="video-thumbnail">
                </div>
                <div class="flex-1 flex flex-col">
                  <h4 class="video-title mb-2" onclick="openVideo('https://www.douyin.com/video/7506096566928674092')">AI工具大全：提高工作效率必备的10款人工智能软件</h4>
                  <div class="flex items-center justify-between text-xs opacity-60 mb-3">
                    <span class="author-name" onclick="openAuthorPage('@科技评测师'); event.stopPropagation();">@科技评测师</span>
                    <div class="flex items-center gap-1">
                      <span class="time-add">9小时前添加</span>
                      <span class="time-publish" style="display:none">10小时前发布</span>
                      <span>·</span>
                      <span>监控关键词</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs mt-auto">
                    <div class="flex gap-3">
                      <span class="stats-item"><i class="fas fa-heart"></i>4.2万</span>
                      <span class="stats-item"><i class="fas fa-comment"></i>738</span>
                      <span class="stats-item"><i class="fas fa-star"></i>1.8万</span>
                      <span class="stats-item"><i class="fas fa-share"></i>923</span>
                    </div>
                    <button class="btn btn-xs btn-outline import-btn imported" onclick="event.stopPropagation();">已导入</button>
                  </div>
                </div>
              </div>
              
              <!-- 灵感项目6 -->
              <div class="inspiration-card bg-base-100 p-3 flex flex-col" data-add-time="2024-01-20T03:45:00" data-publish-time="2024-01-20T10:45:00" data-type="keyword" data-imported="false">
                <div class="inspiration-item mb-3" onclick="openVideo('https://www.douyin.com/video/7506096566928674093')">
                  <div class="viral-index">尚未更新热度指数</div>
                  <img src="https://p9-pc-sign.douyinpic.com/image-cut-tos/010a48ad43bfb86d9667ad7dfefd2207~tplv-dy-resize-walign-pc:640:q80.webp?from=**********&x-expires=**********&x-signature=oDMSr1Golsu6iSG0GRuehFnYq9c%3D" alt="视频缩略图" class="video-thumbnail">
                </div>
                <div class="flex-1 flex flex-col">
                  <h4 class="video-title mb-2" onclick="openVideo('https://www.douyin.com/video/7506096566928674093')">理财小白入门攻略</h4>
                  <div class="flex items-center justify-between text-xs opacity-60 mb-3">
                    <span class="author-name" onclick="openAuthorPage('@理财顾问'); event.stopPropagation();">@理财顾问</span>
                    <div class="flex items-center gap-1">
                      <span class="time-add">10小时前添加</span>
                      <span class="time-publish" style="display:none">4小时前发布</span>
                      <span>·</span>
                      <span>监控关键词</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs mt-auto">
                    <div class="flex gap-3">
                      <span class="stats-item"><i class="fas fa-heart"></i>2.5万</span>
                      <span class="stats-item"><i class="fas fa-comment"></i>412</span>
                      <span class="stats-item"><i class="fas fa-star"></i>9.8千</span>
                      <span class="stats-item"><i class="fas fa-share"></i>567</span>
                    </div>
                    <button class="btn btn-xs btn-outline import-btn" onclick="showImportModal(this, '理财小白入门攻略'); event.stopPropagation();">导入素材库</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center menu-active">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">20</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 导入素材库确认模态框 -->
  <dialog id="import_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">导入素材库</h3>
      <p class="mb-4">确认将「<span id="import-video-title" class="font-medium"></span>」导入素材库吗？</p>
      <div class="alert alert-info mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="h-6 w-6 shrink-0 stroke-current">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
        </svg>
        <span>导入素材库后将开始进行拆片和选题生成</span>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="confirmImport()">确认导入</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <script>
    // 排序功能
    let currentSortType = 'add-time'; // 默认按添加时间排序
    let currentFilterType = 'all'; // 默认显示全部
    let currentImportButton = null; // 当前要导入的按钮
    
    // 筛选功能
    function filterByType(filterType) {
      if (currentFilterType === filterType) return; // 如果已经是当前筛选方式，不执行
      
      currentFilterType = filterType;
      updateFilterButtons();
      applyFilter();
      
      const filterNames = {
        'all': '全部',
        'favorite': '收藏夹',
        'account': '监控账号',
        'keyword': '监控关键词'
      };
      
      showToast(`已筛选：${filterNames[filterType]}`, 'info');
    }
    
    // 应用筛选
    function applyFilter() {
      const cards = document.querySelectorAll('.inspiration-card');
      
      cards.forEach(card => {
        const cardType = card.getAttribute('data-type');
        
        if (currentFilterType === 'all' || cardType === currentFilterType) {
          card.style.display = '';
        } else {
          card.style.display = 'none';
        }
      });
    }
    
    // 更新筛选按钮状态
    function updateFilterButtons() {
      const filterButtons = {
        'all': document.getElementById('filter-all'),
        'favorite': document.getElementById('filter-favorite'),
        'account': document.getElementById('filter-account'),
        'keyword': document.getElementById('filter-keyword')
      };
      
      // 移除所有活跃状态
      Object.values(filterButtons).forEach(btn => {
        btn.classList.remove('btn-active');
        btn.classList.remove('btn-outline');
        btn.classList.add('btn-ghost');
      });
      
      // 添加当前筛选按钮的活跃状态
      const activeBtn = filterButtons[currentFilterType];
      if (activeBtn) {
        activeBtn.classList.add('btn-active');
        activeBtn.classList.add('btn-outline');
        activeBtn.classList.remove('btn-ghost');
      }
    }
    
    // 按添加时间排序
    function sortByAddTime() {
      if (currentSortType === 'add-time') return; // 如果已经是当前排序方式，不执行
      
      currentSortType = 'add-time';
      updateSortButtons();
      sortInspirationCards('data-add-time');
      updateTimeLabels('add');
      showToast('已按添加时间排序', 'info');
    }
    
    // 按发布时间排序
    function sortByPublishTime() {
      if (currentSortType === 'publish-time') return; // 如果已经是当前排序方式，不执行
      
      currentSortType = 'publish-time';
      updateSortButtons();
      sortInspirationCards('data-publish-time');
      updateTimeLabels('publish');
      showToast('已按发布时间排序', 'info');
    }
    
    // 更新时间标签显示
    function updateTimeLabels(timeType) {
      const addTimeLabels = document.querySelectorAll('.time-add');
      const publishTimeLabels = document.querySelectorAll('.time-publish');
      
      if (timeType === 'add') {
        addTimeLabels.forEach(label => label.style.display = '');
        publishTimeLabels.forEach(label => label.style.display = 'none');
      } else if (timeType === 'publish') {
        addTimeLabels.forEach(label => label.style.display = 'none');
        publishTimeLabels.forEach(label => label.style.display = '');
      }
    }
    
    // 更新排序按钮状态
    function updateSortButtons() {
      const addTimeBtn = document.getElementById('sort-by-add-time');
      const publishTimeBtn = document.getElementById('sort-by-publish-time');
      
      // 移除所有活跃状态
      addTimeBtn.classList.remove('btn-active');
      publishTimeBtn.classList.remove('btn-active');
      
      // 添加当前排序按钮的活跃状态
      if (currentSortType === 'add-time') {
        addTimeBtn.classList.add('btn-active');
      } else if (currentSortType === 'publish-time') {
        publishTimeBtn.classList.add('btn-active');
      }
    }
    
    // 排序灵感卡片
    function sortInspirationCards(timeAttribute) {
      const grid = document.getElementById('inspiration-grid');
      const cards = Array.from(grid.querySelectorAll('.inspiration-card[data-add-time]'));
      
      // 按时间倒序排序（最新的在前）
      cards.sort((a, b) => {
        const timeA = new Date(a.getAttribute(timeAttribute));
        const timeB = new Date(b.getAttribute(timeAttribute));
        return timeB - timeA; // 倒序：新的在前
      });
      
      // 重新排列DOM元素
      cards.forEach(card => {
        grid.appendChild(card);
      });
    }
    
    // 显示Toast提示
    function showToast(message, type = 'success') {
      const toastConfigs = {
        success: {
          alertClass: 'alert-success',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 1 1-18 0 9 9 0 0118 0z" />
          </svg>`
        },
        info: {
          alertClass: 'alert-info',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="h-6 w-6 shrink-0 stroke-current">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
          </svg>`
        },
        warning: {
          alertClass: 'alert-warning',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>`
        },
        error: {
          alertClass: 'alert-error',
          icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>`
        }
      };
      
      const config = toastConfigs[type] || toastConfigs.success;
      
      const toastContainer = document.createElement('div');
      toastContainer.className = 'toast toast-top toast-end';
      toastContainer.innerHTML = `
        <div role="alert" class="alert ${config.alertClass}">
          ${config.icon}
          <span>${message}</span>
        </div>
      `;
      
      document.body.appendChild(toastContainer);
      
      setTimeout(() => {
        toastContainer.style.opacity = '1';
      }, 10);
      
      setTimeout(() => {
        toastContainer.style.opacity = '0';
        setTimeout(() => {
          if (toastContainer.parentNode) {
            document.body.removeChild(toastContainer);
          }
        }, 300);
      }, 3000);
    }
    
    // 打开视频链接
    function openVideo(url) {
      window.open(url, '_blank');
    }
    
    // 打开作者主页
    function openAuthorPage(authorName) {
      // 构建作者主页URL（这里使用抖音搜索页面作为示例）
      const searchUrl = `https://www.douyin.com/search/${encodeURIComponent(authorName)}?type=user`;
      window.open(searchUrl, '_blank');
      
      // 显示提示
      showToast(`正在打开 ${authorName} 的主页`, 'info');
    }
    
    // 显示导入素材库确认模态框
    function showImportModal(button, videoTitle) {
      currentImportButton = button;
      document.getElementById('import-video-title').textContent = videoTitle;
      document.getElementById('import_confirm_modal').showModal();
    }
    
    // 确认导入
    function confirmImport() {
      if (currentImportButton) {
        // 更新按钮状态
        currentImportButton.textContent = '已导入';
        currentImportButton.classList.add('imported');
        currentImportButton.onclick = function(e) { e.stopPropagation(); };
        
        // 更新卡片的导入状态
        const card = currentImportButton.closest('.inspiration-card');
        if (card) {
          card.setAttribute('data-imported', 'true');
        }
        
        // 显示成功提示
        showToast('素材已成功导入素材库，开始进行拆片和选题生成', 'success');
        
        // 重置当前按钮引用
        currentImportButton = null;
      }
      
      // 关闭模态框
      document.getElementById('import_confirm_modal').close();
    }
    
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 默认按添加时间排序
      sortInspirationCards('data-add-time');
      updateSortButtons();
      updateTimeLabels('add');
      updateFilterButtons();
      
      // 初始化已导入按钮状态
      initializeImportedButtons();
    });
    
    // 初始化已导入按钮状态
    function initializeImportedButtons() {
      const cards = document.querySelectorAll('.inspiration-card');
      cards.forEach(card => {
        const isImported = card.getAttribute('data-imported') === 'true';
        const importBtn = card.querySelector('.import-btn');
        
        if (isImported && importBtn) {
          importBtn.textContent = '已导入';
          importBtn.classList.add('imported');
          importBtn.onclick = function(e) { e.stopPropagation(); };
        }
      });
    }
  </script>
</body>
</html> 