/*! daisyUI v5.0.19 | MIT License | https://daisyui.com */

/* 主题变量 */
:root {
  --primary: #3b82f6;
  --primary-content: #ffffff;
  --secondary: #1e40af;
  --secondary-content: #ffffff;
  --accent: #22d3ee;
  --accent-content: #ffffff;
  --neutral: #374151;
  --neutral-content: #ffffff;
  --base-100: #ffffff;
  --base-200: #f9fafb;
  --base-300: #d1d5db;
  --base-content: #1f2937;
  --info: #0ea5e9;
  --info-content: #ffffff;
  --success: #10b981;
  --success-content: #ffffff;
  --warning: #f59e0b;
  --warning-content: #ffffff;
  --error: #ef4444;
  --error-content: #ffffff;
  
  --rounded-box: 0.5rem;
  --rounded-btn: 0.25rem;
  --rounded-badge: 9999px;
  
  --animation-btn: 0.25s;
  --animation-input: 0.2s;
  
  --btn-text-case: uppercase;
  --btn-focus-scale: 0.95;
  
  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;
}

/* DaisyUI组件 */

/* 按钮 */
.btn {
  display: inline-flex;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  border-color: transparent;
  border-color: var(--border-btn);
  text-align: center;
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: 0.15s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: var(--rounded-btn);
  height: 3rem;
  min-height: 3rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  text-transform: var(--btn-text-case);
  text-decoration: none;
  border-width: var(--border-btn);
  animation: button-pop var(--animation-btn) ease-out;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  @apply justify-center;
}

.btn-primary {
  --tw-bg-opacity: 1;
  background-color: var(--primary);
  --tw-border-opacity: 1;
  border-color: var(--primary);
  --tw-text-opacity: 1;
  color: var(--primary-content);
}

.btn-ghost {
  border-width: 1px;
  border-color: transparent;
  background-color: transparent;
  color: currentColor;
  box-shadow: none;
}

.btn-square {
  height: 3rem;
  width: 3rem;
  padding: 0;
}

.btn-sm {
  height: 2rem;
  min-height: 2rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
}

/* 卡片 */
.card {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  border-radius: var(--rounded-box);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.card-body {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  padding: 2rem;
  gap: 0.5rem;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}

.card-actions {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 0.5rem;
}

/* 导航和菜单 */
.menu {
  padding: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  width: 100%;
}

.menu a {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: var(--rounded-btn);
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.menu li {
  width: 100%;
}

.menu svg {
  flex-shrink: 0;
}

.menu a.active {
  --tw-bg-opacity: 1;
  background-color: var(--primary);
  --tw-text-opacity: 1;
  color: var(--primary-content);
}

.menu a:hover {
  --tw-bg-opacity: 1;
  background-color: var(--base-300);
}

/* Alert */
.alert {
  display: grid;
  grid-auto-flow: column;
  align-content: center;
  align-items: center;
  justify-items: start;
  grid-template-columns: auto minmax(0, 1fr) auto;
  gap: 1rem;
  text-align: left;
  border-radius: var(--rounded-box);
  padding: 1rem;
  --tw-bg-opacity: 1;
  --tw-text-opacity: 1;
  --tw-bg-opacity: 0.1;
}

.alert-info {
  background-color: rgba(var(--info) / var(--tw-bg-opacity, 1));
  border-color: rgba(var(--info) / var(--tw-border-opacity, 0.2));
  color: var(--info-content);
}

/* Navbar */
.navbar {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  width: 100%;
  min-height: 4rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.navbar-start {
  width: 50%;
  display: flex;
  justify-content: flex-start;
}

.navbar-center {
  flex-shrink: 0;
}

.navbar-end {
  width: 50%;
  display: flex;
  justify-content: flex-end;
}

/* 表单控件 */
.form-control {
  display: flex;
  flex-direction: column;
}

.label {
  display: flex;
  user-select: none;
  align-items: center;
  justify-content: space-between;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.label-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: currentColor;
}

.input {
  flex-shrink: 1;
  height: 3rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1rem;
  line-height: 2;
  border-width: 1px;
  border-color: var(--base-300);
  border-radius: var(--rounded-btn);
  background-color: transparent;
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.input:focus {
  outline: 2px solid hsla(var(--bc) / 0.2);
  outline-offset: 2px;
}

.input-bordered {
  border-color: var(--base-300);
}

.select {
  display: inline-flex;
  cursor: pointer;
  user-select: none;
  appearance: none;
  height: 3rem;
  padding-left: 1rem;
  padding-right: 2.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  min-height: 3rem;
  border-width: 1px;
  border-color: transparent;
  border-radius: var(--rounded-btn);
  background-image: linear-gradient(45deg, transparent 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, transparent 50%);
  background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16px) calc(1px + 50%);
  background-size: 4px 4px, 4px 4px;
  background-repeat: no-repeat;
}

.select-bordered {
  border-color: var(--base-300);
}

/* 表格 */
.table {
  position: relative;
  width: 100%;
  text-align: left;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-collapse: collapse;
}

.table th {
  padding: 1rem;
  vertical-align: middle;
  font-weight: 600;
  white-space: nowrap;
  border-bottom-width: 1px;
  border-color: var(--base-300);
}

.table tr {
  border-bottom-width: 1px;
  border-color: var(--base-300);
}

.table td {
  padding: 1rem;
  vertical-align: middle;
}

.table-zebra tr:nth-child(even) {
  background-color: var(--base-200);
}

/* 抽屉组件 */
.drawer {
  position: relative;
  display: grid;
  grid-auto-columns: max-content auto;
  width: 100%;
}

.drawer-toggle {
  position: absolute;
  height: 0px;
  width: 0px;
  appearance: none;
  opacity: 0;
}

.drawer-toggle:checked ~ .drawer-side {
  pointer-events: auto;
  visibility: visible;
}

.drawer-toggle:checked ~ .drawer-side > .drawer-overlay {
  background-color: rgba(0, 0, 0, 0.4);
}

.drawer-content {
  grid-column-start: 2;
  grid-row-start: 1;
  overflow-y: auto;
  z-index: 0;
  display: flex;
  flex-direction: column;
}

.drawer-side {
  pointer-events: none;
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 10;
  display: grid;
  grid-auto-columns: max-content auto;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  visibility: hidden;
}

.drawer-overlay {
  grid-column-start: 2;
  grid-row-start: 1;
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 10;
  display: block;
  height: 100%;
  width: 100%;
  cursor: pointer;
  background-color: transparent;
}

/* 分页和join */
.join {
  display: inline-flex;
  border-radius: var(--rounded-btn);
  overflow: hidden;
}

.join-item {
  border-radius: 0px;
}

.join-item:first-child {
  border-top-left-radius: var(--rounded-btn);
  border-bottom-left-radius: var(--rounded-btn);
}

.join-item:last-child {
  border-top-right-radius: var(--rounded-btn);
  border-bottom-right-radius: var(--rounded-btn);
}

/* Avatar组件 */
.avatar {
  position: relative;
  display: inline-flex;
}

.avatar > div {
  display: block;
  aspect-ratio: 1 / 1;
  overflow: hidden;
}

.avatar img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.rounded {
  border-radius: var(--rounded-btn);
}

/* Dropdown组件 */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  position: absolute;
  z-index: 10;
  min-width: 11rem;
  transform: translateY(-0.25rem);
  opacity: 0;
  visibility: hidden;
}

.dropdown:is(:focus-within, :hover) .dropdown-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-top .dropdown-content {
  bottom: calc(100% + 0.25rem);
  transform: translateY(0.25rem);
}

.dropdown-top:is(:focus-within, :hover) .dropdown-content {
  transform: translateY(0);
} 