/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  color: #1f2937;
  background-color: #f3f4f6;
}

/* 布局 */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
  width: 250px;
  background-color: #ffffff;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100%;
}

.logo {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: bold;
  margin-left: 0.75rem;
}

.nav-menu {
  list-style: none;
  padding: 1rem;
  flex-grow: 1;
}

.nav-menu li {
  margin-bottom: 0.5rem;
}

.nav-menu a {
  display: block;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #4b5563;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.nav-menu a:hover {
  background-color: #f3f4f6;
}

.nav-menu li.active a {
  background-color: #3b82f6;
  color: white;
}

.user-info {
  padding: 1rem;
  display: flex;
  align-items: center;
  border-top: 1px solid #e5e7eb;
}

.avatar {
  width: 2.5rem;
  height: 2.5rem;
  overflow: hidden;
  border-radius: 50%;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  margin-left: 0.75rem;
}

.user-name {
  font-weight: 500;
}

.user-role {
  font-size: 0.75rem;
  color: #6b7280;
}

/* 主内容区 */
.main-content {
  flex: 1;
  margin-left: 250px;
  padding: 1.5rem;
}

header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

/* 卡片网格 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
}

@media (min-width: 640px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.card-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.card-content p {
  color: #6b7280;
  margin-bottom: 1rem;
  flex-grow: 1;
}

/* 按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  background-color: #e5e7eb;
  color: #4b5563;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:hover {
  background-color: #d1d5db;
}

.btn.primary {
  background-color: #3b82f6;
  color: white;
}

.btn.primary:hover {
  background-color: #2563eb;
}

.btn.small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn.danger {
  background-color: #ef4444;
  color: white;
}

.btn.danger:hover {
  background-color: #dc2626;
}

.btn.page-btn {
  min-width: 2rem;
  margin: 0 0.25rem;
}

.btn.page-btn.active {
  background-color: #3b82f6;
  color: white;
}

/* 操作区 */
.action-bar {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .action-bar {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.alert {
  background-color: #e0f2fe;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #0ea5e9;
}

/* 筛选器 */
.filter-panel {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .filter-panel {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .filter-panel {
    grid-template-columns: repeat(3, 1fr);
  }
}

.filter-item label {
  display: block;
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.filter-item select,
.filter-item input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
}

/* 表格 */
.table-container {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 0.75rem 1rem;
  text-align: left;
}

th {
  background-color: #f9fafb;
  font-weight: 600;
  font-size: 0.875rem;
  color: #4b5563;
}

tr {
  border-bottom: 1px solid #e5e7eb;
}

tr:last-child {
  border-bottom: none;
}

tr:nth-child(even) {
  background-color: #f9fafb;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
}

/* 占位内容 */
.placeholder-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.coming-soon {
  text-align: center;
  color: #6b7280;
}

.coming-soon h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

/* 响应式 */
@media (max-width: 640px) {
  .sidebar {
    width: 80px;
  }
  
  .logo-text,
  .user-details {
    display: none;
  }
  
  .nav-menu a {
    padding: 0.75rem;
    display: flex;
    justify-content: center;
  }
  
  .main-content {
    margin-left: 80px;
  }
} 