<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>选题库 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    
    /* 移动设备优化 */
    @media (max-width: 640px) {
      .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
      }
    }
    
    /* 骨架屏样式 */
    .hidden-content {
      display: none;
    }
    
    /* 闪烁效果 */
    @keyframes pulse {
      0% {
        opacity: 0.6;
      }
      50% {
        opacity: 1;
      }
      100% {
        opacity: 0.6;
      }
    }
    
    .skeleton {
      animation: pulse 1.5s infinite;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 0.375rem;
    }
    
    /* 标签样式 */
    .tag-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.25rem;
    }
    
    .tag-item {
      font-size: 0.7rem;
      padding: 0.15rem 0.4rem;
      border-radius: 1rem;
      background-color: #f3f4f6;
      color: #4b5563;
      white-space: nowrap;
    }
    
    /* 表格相关全局样式 */
    .table-responsive {
      display: block;
      width: 100%;
      overflow-x: auto;
    }
    
    /* 表格操作按钮样式 */
    .table-action-btn {
      min-width: 110px;
      white-space: nowrap;
    }
    
    /* 列宽全局设置 - 移除，使用Tailwind类替代 */
    /* 仅保留操作列的固定宽度，其他列通过Tailwind控制 */
    .col-action {
      min-width: 110px;
      width: 110px;
    }
    
    /* 移动端标签容器最多显示2行 */
    @media (max-width: 768px) {
      .tag-container {
        max-height: 2.5rem;
        overflow: hidden;
      }
      
      /* 移动环境下增加爆款元素列宽度 */
      .col-elements {
        min-width: 180px !important;
      }
    }
    
    /* 选题标题样式 - 允许两行显示 */
    .topic-title {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.4;
      min-height: 2.8em;
      max-width: 100%;
      word-break: break-word;
    }
    
    /* 子表格样式 */
    .subtable {
      width: 100%;
      font-size: 0.9rem;
    }
    
    .subtable th {
      font-weight: 500;
      font-size: 0.85rem;
      color: rgba(0, 0, 0, 0.6);
    }
    
    /* 选题表格垂直居中 */
    .topic-cell {
      display: flex;
      align-items: center;
      height: 100%;
    }
    
    /* 添加Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
    
    /* 移动端表格样式优化 */
    @media (max-width: 768px) {
      .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      }
      
      /* 移动端按钮样式适配 */
      .table-responsive .btn {
        white-space: nowrap;
        padding-left: 0.5rem;
        padding-right: 0.5rem;
        min-width: auto;
      }
      
      /* 确保表格可以水平滚动 */
      .table-responsive table {
        width: auto !important;
        min-width: 100% !important;
      }
      
      /* 确保所有单元格不换行 */
      .table-responsive th,
      .table-responsive td {
        white-space: nowrap;
      }
    }
    
    /* pad尺寸适配 (768px-1024px) */
    @media (min-width: 769px) and (max-width: 1024px) {
      .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
      }
      
      /* 确保按钮完整显示 */
      .table-responsive .btn {
        white-space: nowrap;
      }
    }
    
    /* 常规尺寸适配 (1025px-1280px) */
    @media (min-width: 1025px) and (max-width: 1280px) {
      .table-responsive {
        width: 100%;
      }
    }
    
    /* 进度条指示器样式 */
    .progress-indicator {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.35rem 0.75rem;
      border-radius: 0.5rem;
      background-color: rgba(0, 0, 0, 0.05);
      color: rgba(0, 0, 0, 0.7);
      font-size: 0.875rem;
    }
    
    .progress-indicator .spinner {
      animation: spin 1.5s linear infinite;
      width: 1rem;
      height: 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">选题库</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <header class="mb-6">
          <h1 class="text-2xl font-bold hidden lg:block">选题库</h1>
          <p class="text-base-content opacity-60 hidden lg:block">从素材库中精选并确认的优质选题集合</p>
        </header>
        
        <!-- 操作和筛选区 -->
        <div class="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div class="flex flex-wrap gap-2 items-center">
            <div class="flex items-center gap-1">
              <span class="text-sm opacity-70">在素材库确认的选题会在这里展示</span>
            </div>
          </div>
          
          <div class="flex-shrink-0">
            <div class="join">
              <input type="text" placeholder="搜索选题标题或脚本类型" class="input input-bordered join-item w-64 md:w-80 lg:w-96" />
              <button class="btn join-item">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 选题展示区 -->
        <div class="mb-6">
          <h2 class="text-lg font-semibold mb-3">已确认的选题</h2>
          
          <!-- 选题骨架屏 -->
          <div id="skeleton-topics">
            <div class="overflow-x-auto rounded-box border border-gray-200 bg-base-100 table-responsive">
              <table class="table w-full">
                <thead>
                  <tr>
                    <th class="w-80"><div class="skeleton h-4 w-20"></div></th>
                    <th class="w-32"><div class="skeleton h-4 w-20"></div></th>
                    <th class="w-24"><div class="skeleton h-4 w-16"></div></th>
                    <th class="w-40"><div class="skeleton h-4 w-16"></div></th>
                    <th class="w-48"><div class="skeleton h-4 w-24"></div></th>
                    <th class="w-40"><div class="skeleton h-4 w-20"></div></th>
                    <th class="w-32"><div class="skeleton h-4 w-16"></div></th>
                    <th class="w-48"><div class="skeleton h-4 w-12"></div></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><div class="skeleton h-4 w-28"></div></td>
                    <td><div class="skeleton h-4 w-24"></div></td>
                    <td><div class="skeleton h-4 w-12"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="skeleton h-8 w-8 rounded-full"></div>
                        <div class="skeleton h-4 w-16"></div>
                      </div>
                    </td>
                    <td><div class="skeleton h-4 w-36"></div></td>
                    <td><div class="skeleton h-6 w-20"></div></td>
                  </tr>
                  <tr>
                    <td><div class="skeleton h-4 w-28"></div></td>
                    <td><div class="skeleton h-4 w-24"></div></td>
                    <td><div class="skeleton h-4 w-12"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="skeleton h-8 w-8 rounded-full"></div>
                        <div class="skeleton h-4 w-16"></div>
                      </div>
                    </td>
                    <td><div class="skeleton h-4 w-36"></div></td>
                    <td><div class="skeleton h-6 w-20"></div></td>
                  </tr>
                  <tr>
                    <td><div class="skeleton h-4 w-28"></div></td>
                    <td><div class="skeleton h-4 w-24"></div></td>
                    <td><div class="skeleton h-4 w-12"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="skeleton h-8 w-8 rounded-full"></div>
                        <div class="skeleton h-4 w-16"></div>
                      </div>
                    </td>
                    <td><div class="skeleton h-4 w-36"></div></td>
                    <td><div class="skeleton h-6 w-20"></div></td>
                  </tr>
                  <tr>
                    <td><div class="skeleton h-4 w-28"></div></td>
                    <td><div class="skeleton h-4 w-24"></div></td>
                    <td><div class="skeleton h-4 w-12"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="skeleton h-8 w-8 rounded-full"></div>
                        <div class="skeleton h-4 w-16"></div>
                      </div>
                    </td>
                    <td><div class="skeleton h-4 w-36"></div></td>
                    <td><div class="skeleton h-6 w-20"></div></td>
                  </tr>
                  <tr>
                    <td><div class="skeleton h-4 w-28"></div></td>
                    <td><div class="skeleton h-4 w-24"></div></td>
                    <td><div class="skeleton h-4 w-12"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td><div class="skeleton h-4 w-16"></div></td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="skeleton h-8 w-8 rounded-full"></div>
                        <div class="skeleton h-4 w-16"></div>
                      </div>
                    </td>
                    <td><div class="skeleton h-4 w-36"></div></td>
                    <td><div class="skeleton h-6 w-20"></div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- 实际选题内容 -->
          <div id="topics-content" class="hidden-content">
            <div class="overflow-x-auto rounded-box border border-gray-200 bg-base-100 table-responsive">
              <table class="table table-zebra table-fixed w-full">
                <!-- head -->
                <thead>
                  <tr>
                    <th class="w-80">选题标题</th>
                    <th class="w-32">脚本类型</th>
                    <th class="w-24">选题说明</th>
                    <th class="w-40">爆款元素</th>
                    <th class="w-48">参考素材</th>
                    <th class="w-40">作者</th>
                    <th class="w-32">添加时间</th>
                    <th class="w-48">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- 选题1 -->
                  <tr class="hover:bg-base-300" data-topic-id="T001">
                    <td title="AI工具避坑指南 - 如何选择真正有价值的AI工具，避免被割韭菜">
                      <div class="topic-title topic-cell font-medium">AI工具避坑指南 - 如何选择真正有价值的AI工具</div>
                    </td>
                    <td>聊观点脚本</td>
                    <td>
                      <a href="#" class="link link-hover" onclick="openTopicDescription('T001'); event.stopPropagation(); return false;">查看</a>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">AI工具</span>
                        <span class="tag-item">避坑指南</span>
                        <span class="tag-item">效率</span>
                      </div>
                    </td>
                    <td class="truncate" title="如何提高工作效率？5个实用小技巧 #效率 #工作 #时间管理">
                      <a href="https://www.douyin.com/video/7506096566928674088" target="_blank" class="text-gray-900 hover:underline">如何提高工作效率？5个实用小技巧 #效率...</a>
                    </td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="avatar">
                          <div class="w-6 h-6 rounded-full">
                            <img src="https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLrwwwJUEO7NKCU2py3PtZGG4PhNUcXxcjGFHEUgKJ4MV1mic1SakgHicPkiaIlYTJgunTPnJ393oFjw/132" alt="作者头像">
                          </div>
                        </div>
                        <span class="text-sm">崔正不焦虑</span>
                      </div>
                    </td>
                    <td>2023-11-15</td>
                    <td>
                      <div class="flex gap-2">
                        <button class="btn btn-outline btn-sm table-action-btn" onclick="openTopicDetail('T001')">查看文案</button>
                        <button class="btn btn-outline btn-sm" onclick="openDeleteConfirm('T001')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- 选题2 -->
                  <tr class="hover:bg-base-300" data-topic-id="T002">
                    <td title="从零开始学习编程：初学者全面指南 - 突破技术壁垒，打开编程世界的大门">
                      <div class="topic-title topic-cell font-medium">从零开始学习编程：初学者全面指南 - 突破技术壁垒</div>
                    </td>
                    <td>教程型脚本</td>
                    <td>
                      <a href="#" class="link link-hover" onclick="openTopicDescription('T002'); event.stopPropagation(); return false;">查看</a>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">入门</span>
                        <span class="tag-item">编程</span>
                        <span class="tag-item">技能</span>
                      </div>
                    </td>
                    <td class="truncate" title="5分钟学会Excel高效办公技巧 #Excel #办公技巧 #数据处理">
                      <a href="https://www.douyin.com/video/7506096566928674088" target="_blank" class="text-gray-900 hover:underline">5分钟学会Excel高效办公技巧 #Excel...</a>
                    </td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="avatar">
                          <div class="w-6 h-6 rounded-full">
                            <img src="https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLrwwwJUEO7NKCU2py3PtZGG4PhNUcXxcjGFHEUgKJ4MV1mic1SakgHicPkiaIlYTJgunTPnJ393oFjw/132" alt="作者头像">
                          </div>
                        </div>
                        <span class="text-sm">崔正不焦虑</span>
                      </div>
                    </td>
                    <td>2023-12-03</td>
                    <td>
                      <div class="progress-indicator">
                        <svg class="spinner" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="12" cy="12" r="8" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="30 50"></circle>
                        </svg>
                        <span>文案生成中</span>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- 选题3 -->
                  <tr class="hover:bg-base-300" data-topic-id="T003">
                    <td title="心理健康：打破职场倦怠的实用方法 - 从内在调整到外部支持系统的全方位解决方案">
                      <div class="topic-title topic-cell font-medium">心理健康：打破职场倦怠的实用方法 - 从内在调整到外部支持系统</div>
                    </td>
                    <td>解体型教知识脚本</td>
                    <td>
                      <a href="#" class="link link-hover" onclick="openTopicDescription('T003'); event.stopPropagation(); return false;">查看</a>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">心理</span>
                        <span class="tag-item">职场</span>
                        <span class="tag-item">健康</span>
                      </div>
                    </td>
                    <td class="truncate" title="《欣赏优秀视频之强记女工，全女维修团队真的太棒了》：视频不想做可以不做，没必要硬夸# 孙笑川 # 抽象 # 逆天">
                      <a href="https://www.douyin.com/video/7506096566928674088" target="_blank" class="text-gray-900 hover:underline">《欣赏优秀视频之强记女工，全女维修团队...</a>
                    </td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="avatar">
                          <div class="w-6 h-6 rounded-full">
                            <img src="https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLrwwwJUEO7NKCU2py3PtZGG4PhNUcXxcjGFHEUgKJ4MV1mic1SakgHicPkiaIlYTJgunTPnJ393oFjw/132" alt="作者头像">
                          </div>
                        </div>
                        <span class="text-sm">崔正不焦虑</span>
                      </div>
                    </td>
                    <td>2023-12-18</td>
                    <td>
                      <div class="flex gap-2">
                        <button class="btn btn-outline btn-sm table-action-btn" onclick="openTopicDetail('T003')">查看文案</button>
                        <button class="btn btn-outline btn-sm" onclick="openDeleteConfirm('T003')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- 选题4 -->
                  <tr class="hover:bg-base-300" data-topic-id="T004">
                    <td title="投资入门：构建长期财富的基础知识 - 从零开始的资产配置与风险管理策略">
                      <div class="topic-title topic-cell font-medium">投资入门：构建长期财富的基础知识 - 从零开始的资产配置</div>
                    </td>
                    <td>知识分享脚本</td>
                    <td>
                      <a href="#" class="link link-hover" onclick="openTopicDescription('T004'); event.stopPropagation(); return false;">查看</a>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">投资</span>
                        <span class="tag-item">财富</span>
                        <span class="tag-item">入门</span>
                      </div>
                    </td>
                    <td class="truncate" title="数据分析入门指南：从零开始学习数据分析 #数据分析 #入门 #教程">
                      <a href="https://www.douyin.com/video/7506096566928674088" target="_blank" class="text-gray-900 hover:underline">数据分析入门指南：从零开始学习数据分析...</a>
                    </td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="avatar">
                          <div class="w-6 h-6 rounded-full">
                            <img src="https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLrwwwJUEO7NKCU2py3PtZGG4PhNUcXxcjGFHEUgKJ4MV1mic1SakgHicPkiaIlYTJgunTPnJ393oFjw/132" alt="作者头像">
                          </div>
                        </div>
                        <span class="text-sm">崔正不焦虑</span>
                      </div>
                    </td>
                    <td>2024-01-05</td>
                    <td>
                      <div class="flex gap-2">
                        <button class="btn btn-outline btn-sm table-action-btn" onclick="openTopicDetail('T004')">查看文案</button>
                        <button class="btn btn-outline btn-sm" onclick="openDeleteConfirm('T004')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- 选题5 -->
                  <tr class="hover:bg-base-300" data-topic-id="T005">
                    <td title="可持续时尚：环保服装品牌与购买指南 - 时尚与环保并行，重新定义消费理念">
                      <div class="topic-title topic-cell font-medium">可持续时尚：环保服装品牌与购买指南 - 时尚与环保并行</div>
                    </td>
                    <td>揭秘型教知识脚本</td>
                    <td>
                      <a href="#" class="link link-hover" onclick="openTopicDescription('T005'); event.stopPropagation(); return false;">查看</a>
                    </td>
                    <td>
                      <div class="tag-container">
                        <span class="tag-item">环保</span>
                        <span class="tag-item">时尚</span>
                        <span class="tag-item">指南</span>
                      </div>
                    </td>
                    <td class="truncate" title="新房装修完，怎么除甲醛？#装修 #装修避坑 #除甲醛 #桃气炭长 #桃气出击醛菌覆灭">
                      <a href="https://www.douyin.com/video/7506096566928674088" target="_blank" class="text-gray-900 hover:underline">新房装修完，怎么除甲醛？#装修 #装修避坑...</a>
                    </td>
                    <td>
                      <div class="flex items-center gap-2">
                        <div class="avatar">
                          <div class="w-6 h-6 rounded-full">
                            <img src="https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLrwwwJUEO7NKCU2py3PtZGG4PhNUcXxcjGFHEUgKJ4MV1mic1SakgHicPkiaIlYTJgunTPnJ393oFjw/132" alt="作者头像">
                          </div>
                        </div>
                        <span class="text-sm">崔正不焦虑</span>
                      </div>
                    </td>
                    <td>2024-01-20</td>
                    <td>
                      <div class="flex gap-2">
                        <button class="btn btn-outline btn-sm table-action-btn" onclick="openTopicDetail('T005')">查看文案</button>
                        <button class="btn btn-outline btn-sm" onclick="openDeleteConfirm('T005')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <!-- 分页控制 -->
        <div class="flex justify-center mb-6">
          <div class="join">
            <button class="join-item btn btn-sm">«</button>
            <button class="join-item btn btn-sm">‹</button>
            <button class="join-item btn btn-sm">1</button>
            <button class="join-item btn btn-sm btn-active">2</button>
            <button class="join-item btn btn-sm">3</button>
            <button class="join-item btn btn-sm">4</button>
            <button class="join-item btn btn-sm">›</button>
            <button class="join-item btn btn-sm">»</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">28</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item menu-active w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 创建选题模态框 -->
  <dialog id="create_topic_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">创建选题</h3>
      <div class="mb-4">
        <div class="text-base font-medium mb-2">选题标题</div>
        <input type="text" placeholder="请输入选题标题" class="input input-bordered w-full" />
      </div>
      <div class="mb-4">
        <div class="text-base font-medium mb-2">脚本类型</div>
        <select class="select select-bordered w-full">
          <option disabled selected>请选择脚本类型</option>
          <option>揭秘型教知识脚本</option>
          <option>解说型教知识脚本</option>
          <option>解体型教知识脚本</option>
          <option>教程型脚本</option>
          <option>知识分享脚本</option>
        </select>
      </div>
      <div class="mb-4">
        <div class="text-base font-medium mb-2">选题描述</div>
        <textarea class="textarea textarea-bordered w-full h-24" placeholder="请简要描述选题内容和目标受众"></textarea>
      </div>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn bg-black text-white hover:bg-gray-800">确定</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>

  <!-- 查看选题详情模态框 -->
  <dialog id="topic-detail-modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-w-3xl">
      <h3 class="text-lg font-bold mb-4" id="topic-detail-title">文案说明</h3>
      <div id="topic-detail-content" class="py-2 prose max-w-none">
        <!-- 选题详情将通过JavaScript填充 -->
      </div>
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
        <button class="btn bg-black text-white hover:bg-gray-800" onclick="openEditTopicModal()">修改文案</button>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 修改文案模态框 -->
  <dialog id="edit-topic-modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-w-3xl">
      <div id="edit-topic-content">
        <h3 class="text-lg font-bold mb-4">修改文案</h3>
        <div class="mb-4">
          <p class="text-sm opacity-75 mb-3">请在下方输入修改文案的要求，我们会根据您的需求进行调整</p>
          <textarea id="edit-topic-request" class="textarea textarea-bordered w-full h-32" placeholder="例如：希望文案更加口语化，增加更多生活例子，简化专业术语等" oninput="checkEditInput()"></textarea>
        </div>
        <div class="modal-action">
          <button class="btn" onclick="closeEditTopicModal()">取消</button>
          <button id="submit-edit-btn" class="btn bg-black text-white hover:bg-gray-800" onclick="submitTopicEdit()" disabled>提交</button>
        </div>
      </div>
      <div id="edit-topic-loading" class="hidden flex flex-col items-center justify-center py-10">
        <div class="mb-4">
          <span class="loading loading-spinner loading-lg"></span>
        </div>
        <p class="text-base">正在根据您的要求修改文案，请稍候...</p>
        <p class="text-sm opacity-70 mt-2">预计需要60秒左右</p>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 删除选题确认模态框 -->
  <dialog id="delete-topic-modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">删除选题</h3>
      <p>删除选题后不再选题库内展示，确认取消吗？</p>
      <input type="hidden" id="delete-topic-id" value="">
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white" onclick="deleteTopic()">确认</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <!-- 查看选题说明模态框 -->
  <dialog id="topic-description-modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box max-w-3xl">
      <h3 class="text-lg font-bold mb-4" id="topic-description-title">选题说明</h3>
      <div id="topic-description-content" class="py-2 prose max-w-none">
        <!-- 选题说明内容将通过JavaScript填充 -->
      </div>
      <div class="modal-action">
        <form method="dialog">
          <button class="btn">关闭</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>
  
  <script>
    // 模拟数据
    const topicData = {
      'T001': {
        id: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6',
        title: 'AI工具避坑指南',
        scriptType: '聊观点脚本',
        description: `# AI工具避坑指南

### 逐字稿
- **警惕（前5秒）**：  
  "兄弟，你花2999买的AI工具，用了一个月，写的文案还是像机器人念课文？做的报表改到凌晨三点？我跟你说——市面上90%的AI工具，都是来割你钱包的！"（拍桌，语气急，皱眉）  

- **揭秘（10-20秒）**：  
  "第一种，套模板的'假AI'。我一做餐饮的朋友，花1888买了个'AI菜品故事生成器'，结果生成的文案全是'传承三代老手艺''妈妈的味道'，客户直接说'抄隔壁家的吧？'（摊手摇头）  
  第二种，功能乱堆的'大杂烩'。说是'全能AI'，又是写文案、又是画图、又是做表格，结果写文案不如我手机里的'文案狗'，画图比我家娃用儿童画板画的还差！（比划嫌弃表情）  
  第三种，收费陷阱的'吸血鬼'。说是'永久免费'，用俩月突然弹框'升级VIP，不然功能全锁'，你不充钱？行啊，写一半的方案直接卡成PPT！（模仿手机弹框手势）"  

- **信任（20-30秒）**：  
  "那啥工具才不坑？记好了！第一，得能'贴行业'——你是做电商的，它重点教你写'爆款标题'；你是做咨询的，它专研'客户痛点分析'。第二，操作得'傻瓜化'，别整什么代码、参数，点两下'生成'键，结果直接能交差。第三，售后得'活人管'，不会用？打个语音，客服手把手教你调参数！（竖三根手指，语气坚定）  
  我自己用的智能体工具，就这三点全占——上周帮我写的电商标题，直接让店铺流量涨了30%，真不是广告，是真能帮咱赚钱！"（拍胸脯，眼神真诚）  

- **行动（30-40秒）**：  
  "想避坑的，评论区扣'工具测评'，我发你《AI工具避坑红黑榜》，还有我在用的工具链接。记住了，钱要花在刀刃上，别让这些'割韭菜'的工具，耽误你搞钱！（指镜头，语气加重）"  


### 脚本自检结果
- **脚本类型匹配**：符合"聊观点脚本（基于行业和产品/从业者）"，选题为"AI工具行业乱象批判+推荐"，结构为"问题揭露-解决方案-转化引导"。  
- **爆款元素应用**：使用"最差（割韭菜）""人群（职场/创业者）"元素，精准锁定目标用户痛点。  
- **情绪曲线设计**：警惕（危机）→揭秘（乱象）→信任（专业）→行动（转化），符合"情绪波点"要求，有起伏且连贯。  


### 文案自检结果
- **画面感**：  
  - 具体场景描述："写的文案像机器人念课文""做的报表改到凌晨三点""客户说'抄隔壁家的吧？'"等，通过细节还原用户真实体验，画面感强。  
  - 类比/对比："画图比我家娃用儿童画板画的还差！"等生动比喻增强表现力。

- **口语化表达**：  
  - 贴合目标人群语言风格："兄弟""我跟你说""割你钱包的""不是广告，是真能帮咱赚钱"等口语化表达，增强亲和力。
  - 动作提示丰富：拍桌、皱眉、摊手摇头等表情动作提示，有助于表演自然流畅。

- **转化设计**：  
  - 低门槛号召："评论区扣'工具测评'"简单易行。
  - 利益明确："《AI工具避坑红黑榜》"和"工具链接"两重福利，增加转化动力。`,
        referMaterial: '如何提高工作效率？5个实用小技巧 #效率 #工作 #时间管理',
        createTime: '2023-11-15'
      },
      'T002': {
        id: 'b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7',
        title: '从零开始学习编程：初学者指南',
        scriptType: '教程型脚本',
        description: `# 从零开始学习编程：初学者指南

## 目标受众
- 编程零基础人群
- 转行想进入IT行业的人士
- 对编程感兴趣的学生

## 内容要点
1. **为什么学习编程**
   - 就业市场需求
   - 思维方式提升
   - 解决问题能力培养

2. **入门语言选择指南**
   - Python：简洁易学，应用广泛
   - JavaScript：网页互动必备
   - 其他语言简介与适用场景

3. **学习路径规划**
   - 基础概念（变量、循环、条件语句）
   - 数据结构与算法入门
   - 项目实践的重要性

4. **学习资源推荐**
   - 免费/付费在线课程对比
   - 优质学习网站与社区
   - 入门书籍推荐

5. **常见学习误区**
   - 过度追求语言数量
   - 忽视基础概念
   - 缺乏实践项目

6. **保持学习动力的方法**
   - 设定小目标
   - 结对编程
   - 参与开源项目`,
        referMaterial: '5分钟学会Excel高效办公技巧 #Excel #办公技巧 #数据处理',
        createTime: '2023-12-03'
      },
      'T003': {
        id: 'c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8',
        title: '心理健康：打破职场倦怠的实用方法',
        scriptType: '解体型教知识脚本',
        description: `# 心理健康：打破职场倦怠的实用方法

## 目标受众
- 高压工作环境中的职场人士
- 感到工作倦怠的员工
- 企业HR与管理者

## 内容要点
1. **识别职场倦怠的信号**
   - 生理信号：持续疲劳、睡眠问题
   - 心理信号：冷漠、情绪波动
   - 行为信号：工作效率下降、拖延

2. **倦怠的科学解释**
   - 压力荷尔蒙长期影响
   - 自主神经系统失衡
   - 认知资源耗竭

3. **工作场景中的自我调节技巧**
   - 微休息法：90分钟工作循环
   - 正念冥想：5分钟减压练习
   - 情绪命名：将感受具体化

4. **工作环境的重构**
   - 物理空间优化
   - 任务管理系统建立
   - 数字排毒策略

5. **与管理层沟通倦怠问题的方法**
   - 准备有效的对话开场
   - 用数据说话：绩效与倦怠关联
   - 共同制定解决方案

6. **长期恢复与预防策略**
   - 建立支持系统
   - 寻找工作意义感
   - 培养工作外的兴趣爱好`,
        referMaterial: '《欣赏优秀视频之强记女工，全女维修团队真的太棒了》：视频不想做可以不做，没必要硬夸# 孙笑川 # 抽象 # 逆天',
        createTime: '2023-12-18'
      },
      'T004': {
        id: 'd4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9',
        title: '投资入门：构建长期财富的基础知识',
        scriptType: '知识分享脚本',
        description: `# 投资入门：构建长期财富的基础知识

## 目标受众
- 投资新手
- 年轻工作者
- 想要理财但没经验的人群

## 内容要点
1. **投资基础概念**
   - 什么是资产与负债
   - 复利的神奇力量
   - 风险与回报的关系

2. **常见投资工具对比**
   - 储蓄：安全但收益低
   - 股票：高风险高回报
   - 债券：稳定收益
   - 基金：分散风险的选择

3. **建立投资心态**
   - 长期思维的重要性
   - 控制情绪避免冲动决策
   - 持续学习与更新知识

4. **制定个人投资计划**
   - 明确财务目标
   - 资产配置策略
   - 定期投资vs择时投资

5. **风险管理技巧**
   - 分散投资组合
   - 设定止损点
   - 应急资金的重要性

6. **开始投资的实际步骤**
   - 选择合适的投资平台
   - 开户流程简介
   - 第一笔投资的选择建议`,
        referMaterial: '数据分析入门指南：从零开始学习数据分析 #数据分析 #入门 #教程',
        createTime: '2024-01-05'
      },
      'T005': {
        id: 'e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0',
        title: '可持续时尚：环保服装品牌与购买指南',
        scriptType: '揭秘型教知识脚本',
        description: `# 可持续时尚：环保服装品牌与购买指南

## 目标受众
- 环保意识较强的消费者
- 对可持续生活方式感兴趣的年轻人
- 时尚爱好者

## 内容要点
1. **快时尚的环境代价**
   - 资源消耗数据
   - 污染与废弃物问题
   - 社会伦理考量

2. **可持续时尚的核心理念**
   - 环保材料选择
   - 公平劳动条件
   - 设计的耐久性

3. **如何识别真正的环保品牌**
   - 关键认证标识解读
   - 绿色洗白的常见手法
   - 透明度评估方法

4. **国内外推荐环保服装品牌**
   - 高端可持续品牌
   - 平价环保选择
   - 二手/复古市场推荐

5. **构建可持续衣橱的实用建议**
   - 精简数量：胶囊衣橱概念
   - 优化购买决策流程
   - 延长服装使用寿命的技巧

6. **参与可持续时尚的其他方式**
   - 租赁平台介绍
   - 衣物交换活动
   - 自我改造与创意重用`,
        referMaterial: '新房装修完，怎么除甲醛？#装修 #装修避坑 #除甲醛 #桃气炭长 #桃气出击醛菌覆灭',
        createTime: '2024-01-20'
      }
    };
    
    // 当前选中的选题ID
    let currentTopicId = '';
    
    // 检查编辑输入框内容
    function checkEditInput() {
      const request = document.getElementById('edit-topic-request').value;
      const submitBtn = document.getElementById('submit-edit-btn');
      submitBtn.disabled = !request.trim();
    }
    
    // 打开选题详情模态框
    function openTopicDetail(id) {
      currentTopicId = id; // 保存当前选中的选题ID
      const modal = document.getElementById('topic-detail-modal');
      const content = document.getElementById('topic-detail-content');
      const data = topicData[id];
      
      content.innerHTML = marked.parse(data.description);
      
      modal.showModal();
    }
    
    // 打开修改文案模态框
    function openEditTopicModal() {
      document.getElementById('topic-detail-modal').close(); // 关闭详情模态框
      
      // 重置修改模态框状态
      document.getElementById('edit-topic-request').value = ''; // 清空文本域
      document.getElementById('submit-edit-btn').disabled = true; // 禁用提交按钮
      document.getElementById('edit-topic-content').classList.remove('hidden'); // 显示编辑内容
      document.getElementById('edit-topic-loading').classList.add('hidden'); // 隐藏加载状态
      
      document.getElementById('edit-topic-modal').showModal(); // 打开修改模态框
    }
    
    // 关闭修改文案模态框
    function closeEditTopicModal() {
      document.getElementById('edit-topic-modal').close(); // 关闭修改模态框
      document.getElementById('topic-detail-modal').showModal(); // 重新打开详情模态框
    }
    
    // 提交文案修改请求
    function submitTopicEdit() {
      const request = document.getElementById('edit-topic-request').value;
      if (!request.trim()) {
        showToast('请输入修改要求');
        return;
      }
      
      // 显示加载状态
      document.getElementById('edit-topic-content').classList.add('hidden');
      document.getElementById('edit-topic-loading').classList.remove('hidden');
      
      // 模拟处理时间（5秒）
      setTimeout(function() {
        // 模拟更新文案内容
        if (currentTopicId && topicData[currentTopicId]) {
          // 根据要求略微修改文案
          let originalContent = topicData[currentTopicId].description;
          
          // 简单示例：给文案添加一个"更新"标记
          if (request.includes('口语化')) {
            originalContent = originalContent.replace(/###/g, '### 【更口语化】');
          } else if (request.includes('例子')) {
            originalContent = originalContent.replace(/###/g, '### 【增加例子】');
          } else {
            originalContent = originalContent.replace(/###/g, '### 【已优化】');
          }
          
          // 更新存储的内容
          topicData[currentTopicId].description = originalContent;
        }
        
        // 关闭修改模态框
        document.getElementById('edit-topic-modal').close();
        
        // 打开详情模态框并显示更新后的内容
        openTopicDetail(currentTopicId);
        
        // 显示成功提示
        showToast('文案已成功更新');
      }, 5000); // 5秒后执行
    }
    
    // 打开删除确认模态框
    function openDeleteConfirm(id) {
      document.getElementById('delete-topic-id').value = id;
      document.getElementById('delete-topic-modal').showModal();
    }
    
    // 执行删除选题
    function deleteTopic() {
      const id = document.getElementById('delete-topic-id').value;
      // 此处实现删除选题的功能，例如从列表中移除
      const row = document.querySelector(`tr[data-topic-id="${id}"]`);
      if (row) {
        row.remove();
        
        // 更新侧边栏徽章数量
        const topicsBadge = document.querySelector('a[href="topics.html"] .badge');
        const currentCount = parseInt(topicsBadge.textContent);
        if (!isNaN(currentCount)) {
          topicsBadge.textContent = currentCount - 1;
        }
        
        // 显示删除成功提示
        showToast('选题已成功删除');
      }
    }
    
    // 打开选题说明模态框
    function openTopicDescription(id) {
      const modal = document.getElementById('topic-description-modal');
      const content = document.getElementById('topic-description-content');
      const data = topicData[id];
      
      // 选题说明内容与选题详情内容相同，但可以根据需要修改
      content.innerHTML = marked.parse(data.description);
      
      modal.showModal();
    }
  </script>
  
  <!-- 引入Marked.js用于Markdown渲染 -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  
  <!-- 骨架屏加载脚本 -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 模拟加载延迟
      setTimeout(function() {
        // 显示选题内容
        const skeletonTopics = document.getElementById('skeleton-topics');
        const topicsContent = document.getElementById('topics-content');
        
        if (skeletonTopics && topicsContent) {
          // 隐藏骨架屏
          skeletonTopics.style.display = 'none';
          
          // 显示选题内容
          topicsContent.classList.remove('hidden-content');
          
          // 更新侧边栏数量
          updateSidebarCounts();
        }
      }, 300); // 300毫秒后显示内容
    });
    
    // 创建脚本函数
    function createScript() {
      // 此处实现创建脚本的功能
      // 显示成功提示
      showToast('脚本已创建成功');
    }
    
    // 显示Toast提示
    function showToast(message) {
      // 创建toast元素
      let toast = document.getElementById('success-toast');
      if (!toast) {
        toast = document.createElement('div');
        toast.id = 'success-toast';
        toast.className = 'toast toast-top toast-end opacity-0 transition-opacity duration-300';
        toast.innerHTML = `
          <div role="alert" class="alert alert-success">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>${message}</span>
          </div>
        `;
        document.body.appendChild(toast);
      } else {
        toast.querySelector('span').textContent = message;
      }
      
      // 显示Toast
      toast.classList.remove('opacity-0');
      toast.classList.add('opacity-100');
      
      // 2秒后自动隐藏
      setTimeout(() => {
        toast.classList.remove('opacity-100');
        toast.classList.add('opacity-0');
      }, 2000);
    }
    
    // 更新侧边栏数量
    function updateSidebarCounts() {
      // 计算素材总数
      const materialCount = 8; // 示例数据
      
      // 计算已确认选题总数
      const topicCount = 5; // 示例数据
      
      // 更新侧边栏徽章
      const materialsBadge = document.querySelector('a[href="assets.html"] .badge');
      const topicsBadge = document.querySelector('a[href="topics.html"] .badge');
      
      if (materialsBadge) {
        materialsBadge.textContent = materialCount;
      }
      
      if (topicsBadge) {
        topicsBadge.textContent = topicCount;
      }
    }
  </script>
  
  <!-- 成功提示Toast -->
  <div id="success-toast" class="toast toast-top toast-end opacity-0 transition-opacity duration-300">
    <div role="alert" class="alert alert-success">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>操作成功</span>
    </div>
  </div>
</body>
</html> 