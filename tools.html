<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能体 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    
    /* 工具卡片式样 */
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1.5rem;
    }
    
    .tool-card {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    .tool-card .card-body {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }
    
    .tool-card .card-actions {
      margin-top: auto;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
    
    /* Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
    
    /* 标签筛选按钮样式 */
    .tag-filter {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
    }
    
    .tag-filter .btn {
      font-size: 0.85rem;
      padding: 0.35rem 0.75rem;
      height: auto;
      min-height: 2rem;
    }
    
    .tag-filter .btn.active {
      background-color: #333;
      color: white;
    }
    
    /* 标签样式 */
    .badge-soft {
      font-weight: normal;
      padding: 0.35rem 0.5rem;
      font-size: 0.7rem;
    }
    
    .card-actions .badge {
      margin-right: 0.25rem;
      margin-top: 0.25rem;
    }
    
    /* 自定义标签颜色 */
    .badge-选题工具 {
      --tw-bg-opacity: 0.2;
      --tw-text-opacity: 1;
      background-color: rgba(var(--p), var(--tw-bg-opacity));
      color: rgba(var(--p), var(--tw-text-opacity));
    }
    
    .badge-文案改写 {
      --tw-bg-opacity: 0.2;
      --tw-text-opacity: 1;
      background-color: rgba(var(--s), var(--tw-bg-opacity));
      color: rgba(var(--s), var(--tw-text-opacity));
    }
    
    .badge-爆款元素 {
      --tw-bg-opacity: 0.2;
      --tw-text-opacity: 1;
      background-color: rgba(var(--a), var(--tw-bg-opacity));
      color: rgba(var(--a), var(--tw-text-opacity));
    }
    
    .badge-开篇工具 {
      --tw-bg-opacity: 0.2;
      --tw-text-opacity: 1;
      background-color: rgba(var(--in), var(--tw-bg-opacity));
      color: rgba(var(--in), var(--tw-text-opacity));
    }
    
    .badge-好变现 {
      --tw-bg-opacity: 0.2;
      --tw-text-opacity: 1;
      background-color: rgba(var(--su), var(--tw-bg-opacity));
      color: rgba(var(--su), var(--tw-text-opacity));
    }
    
    .badge-立人设 {
      --tw-bg-opacity: 0.2;
      --tw-text-opacity: 1;
      background-color: rgba(var(--wa), var(--tw-bg-opacity));
      color: rgba(var(--wa), var(--tw-text-opacity));
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">智能体</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <!-- 标题区 -->
        <header class="mb-6">
          <h1 class="text-2xl font-bold hidden lg:block">智能体</h1>
          <p class="text-base-content opacity-60 hidden lg:block">让每一次创作都是突破，每一个作品都有价值</p>
        </header>
        
        <!-- 标签筛选和搜索区 -->
        <div class="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <!-- 标签筛选 -->
          <div class="tag-filter">
            <button class="btn btn-sm btn-outline active" data-tag="all">全部</button>
            <button class="btn btn-sm btn-outline" data-tag="选题工具">选题工具</button>
            <button class="btn btn-sm btn-outline" data-tag="文案改写">文案改写</button>
            <button class="btn btn-sm btn-outline" data-tag="爆款元素">爆款元素</button>
            <button class="btn btn-sm btn-outline" data-tag="开篇工具">开篇工具</button>
            <button class="btn btn-sm btn-outline" data-tag="好变现">好变现</button>
            <button class="btn btn-sm btn-outline" data-tag="立人设">立人设</button>
          </div>
          
          <!-- 搜索区 -->
          <div class="flex-shrink-0">
            <div class="join">
              <input id="search-input" type="text" placeholder="搜索智能体名称..." class="input input-bordered join-item w-64 md:w-80 lg:w-96" />
              <button id="search-btn" class="btn join-item">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <!-- 工具卡片 -->
          <div class="card bg-base-100 shadow-sm hover:shadow-md transition-shadow" data-tags="选题工具,爆款元素,好变现">
            <div class="card-body p-4">
              <div class="flex items-center mb-3">
                <div class="bg-base-200 rounded-full w-10 h-10 flex items-center justify-center mr-3 overflow-hidden">
                  <img src="./assets/avatar_jinqiangdashu.jpeg" alt="金枪大叔：选题" class="w-full h-full object-cover">
                </div>
                <h3 class="card-title text-base">金枪大叔：选题</h3>
              </div>
              <p class="text-sm opacity-70">金枪大叔是原名岳华平的知名广告策划人、导演和作家，红制作创始人，以独特广告创意和营销理念闻名，代表作品有《借势》，在抖音拥有众多粉丝。</p>
              <div class="card-actions flex-wrap mt-3">
                <div class="badge badge-soft badge-选题工具">选题工具</div>
                <div class="badge badge-soft badge-爆款元素">爆款元素</div>
                <div class="badge badge-soft badge-好变现">好变现</div>
                <button class="btn btn-sm btn-neutral ml-auto" data-tool-name="金枪大叔：选题">使用</button>
              </div>
            </div>
          </div>
          
          <div class="card bg-base-100 shadow-sm hover:shadow-md transition-shadow" data-tags="文案改写,爆款元素,立人设">
            <div class="card-body p-4">
              <div class="flex items-center mb-3">
                <div class="bg-base-200 rounded-full w-10 h-10 flex items-center justify-center mr-3 overflow-hidden">
                  <img src="./assets/avatar_jinqiangdashu.jpeg" alt="金枪大叔：文案改写" class="w-full h-full object-cover">
                </div>
                <h3 class="card-title text-base">金枪大叔：文案改写</h3>
              </div>
              <p class="text-sm opacity-70">金枪大叔是原名岳华平的知名广告策划人、导演和作家，红制作创始人，以独特广告创意和营销理念闻名，代表作品有《借势》，在抖音拥有众多粉丝。</p>
              <div class="card-actions flex-wrap mt-3">
                <div class="badge badge-soft badge-文案改写">文案改写</div>
                <div class="badge badge-soft badge-爆款元素">爆款元素</div>
                <div class="badge badge-soft badge-立人设">立人设</div>
                <button class="btn btn-sm btn-neutral ml-auto" data-tool-name="金枪大叔：文案改写">使用</button>
              </div>
            </div>
          </div>
          
          <div class="card bg-base-100 shadow-sm hover:shadow-md transition-shadow" data-tags="选题工具,开篇工具,好变现">
            <div class="card-body p-4">
              <div class="flex items-center mb-3">
                <div class="bg-base-200 rounded-full w-10 h-10 flex items-center justify-center mr-3 overflow-hidden">
                  <img src="./assets/avatar_fangqi_kiki.jpeg" alt="房琪：选题" class="w-full h-full object-cover">
                </div>
                <h3 class="card-title text-base">房琪：选题</h3>
              </div>
              <p class="text-sm opacity-70">房琪 kiki 是 1993 年出生于黑龙江的中国内地女主持人、旅行博主，以温暖有力量的文案和励志经历走红网络。</p>
              <div class="card-actions flex-wrap mt-3">
                <div class="badge badge-soft badge-选题工具">选题工具</div>
                <div class="badge badge-soft badge-开篇工具">开篇工具</div>
                <div class="badge badge-soft badge-好变现">好变现</div>
                <button class="btn btn-sm btn-neutral ml-auto" data-tool-name="房琪：选题">使用</button>
              </div>
            </div>
          </div>
          
          <div class="card bg-base-100 shadow-sm hover:shadow-md transition-shadow" data-tags="文案改写,立人设,开篇工具,爆款元素">
            <div class="card-body p-4">
              <div class="flex items-center mb-3">
                <div class="bg-base-200 rounded-full w-10 h-10 flex items-center justify-center mr-3 overflow-hidden">
                  <img src="./assets/avatar_fangqi_kiki.jpeg" alt="房琪：文案改写" class="w-full h-full object-cover">
                </div>
                <h3 class="card-title text-base">房琪：文案改写</h3>
              </div>
              <p class="text-sm opacity-70">房琪 kiki 是 1993 年出生于黑龙江的中国内地女主持人、旅行博主，以温暖有力量的文案和励志经历走红网络。</p>
              <div class="card-actions flex-wrap mt-3">
                <div class="badge badge-soft badge-文案改写">文案改写</div>
                <div class="badge badge-soft badge-立人设">立人设</div>
                <div class="badge badge-soft badge-开篇工具">开篇工具</div>
                <div class="badge badge-soft badge-爆款元素">爆款元素</div>
                <button class="btn btn-sm btn-neutral ml-auto" data-tool-name="房琪：文案改写">使用</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">28</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item menu-active w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>

<script>
  // 显示Toast提示
  function showToast(message, type = 'success') {
    // 创建toast元素
    let toast = document.getElementById('toast-message');
    if (!toast) {
      toast = document.createElement('div');
      toast.id = 'toast-message';
      toast.className = 'toast toast-top toast-end opacity-0 transition-opacity duration-300';
      
      let alertClass = 'alert-success';
      if (type === 'error') alertClass = 'alert-error';
      else if (type === 'warning') alertClass = 'alert-warning';
      else if (type === 'info') alertClass = 'alert-info';
      
      toast.innerHTML = `
        <div role="alert" class="alert ${alertClass}">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>${message}</span>
        </div>
      `;
      document.body.appendChild(toast);
    } else {
      toast.querySelector('span').textContent = message;
      
      // 更新alert类型
      let alertDiv = toast.querySelector('.alert');
      alertDiv.classList.remove('alert-success', 'alert-error', 'alert-warning', 'alert-info');
      
      let alertClass = 'alert-success';
      if (type === 'error') alertClass = 'alert-error';
      else if (type === 'warning') alertClass = 'alert-warning';
      else if (type === 'info') alertClass = 'alert-info';
      
      alertDiv.classList.add(alertClass);
    }
    
    // 显示Toast
    toast.classList.remove('opacity-0');
    toast.classList.add('opacity-100');
    
    // 2秒后自动隐藏
    setTimeout(() => {
      toast.classList.remove('opacity-100');
      toast.classList.add('opacity-0');
    }, 2000);
  }
  
  // 使用工具函数
  function useTool(toolName) {
    showToast(`已启动${toolName}智能体`);
  }
  
  // 更新侧边栏数量
  function updateSidebarCounts() {
    // 计算素材总数
    const materialCount = 8; // 示例数据
    
    // 计算已确认选题总数
    const topicCount = 5; // 示例数据
    
    // 更新侧边栏徽章
    const materialsBadge = document.querySelector('a[href="assets.html"] .badge');
    const topicsBadge = document.querySelector('a[href="topics.html"] .badge');
    
    if (materialsBadge) {
      materialsBadge.textContent = materialCount;
    }
    
    if (topicsBadge) {
      topicsBadge.textContent = topicCount;
    }
  }
  
  // 过滤卡片函数
  function filterCards(tag) {
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
      if (tag === 'all' || card.getAttribute('data-tags').includes(tag)) {
        card.style.display = '';
      } else {
        card.style.display = 'none';
      }
    });
  }
  
  // 搜索智能体函数
  function searchTools(query) {
    query = query.toLowerCase().trim();
    const cards = document.querySelectorAll('.card');
    
    cards.forEach(card => {
      const title = card.querySelector('.card-title').textContent.toLowerCase();
      const description = card.querySelector('p.text-sm').textContent.toLowerCase();
      
      if (query === '' || title.includes(query) || description.includes(query)) {
        card.style.display = '';
      } else {
        card.style.display = 'none';
      }
    });
  }
  
  // 页面加载时更新侧边栏数量并初始化事件
  document.addEventListener('DOMContentLoaded', function() {
    updateSidebarCounts();
    
    // 为工具卡片按钮添加事件
    const toolButtons = document.querySelectorAll('[data-tool-name]');
    toolButtons.forEach(button => {
      button.addEventListener('click', function() {
        const toolName = this.getAttribute('data-tool-name');
        if (toolName) {
          useTool(toolName);
        }
      });
    });
    
    // 标签筛选按钮事件
    const tagButtons = document.querySelectorAll('.tag-filter .btn');
    tagButtons.forEach(button => {
      button.addEventListener('click', function() {
        // 移除所有标签按钮的active类
        tagButtons.forEach(btn => btn.classList.remove('active'));
        // 为当前点击的按钮添加active类
        this.classList.add('active');
        
        // 过滤卡片
        const tag = this.getAttribute('data-tag');
        filterCards(tag);
      });
    });
    
    // 搜索功能
    document.getElementById('search-btn').addEventListener('click', function() {
      const query = document.getElementById('search-input').value;
      searchTools(query);
    });
    
    // 回车搜索
    document.getElementById('search-input').addEventListener('keyup', function(event) {
      if (event.key === 'Enter') {
        const query = this.value;
        searchTools(query);
      }
    });
  });
</script>

<!-- 成功提示Toast -->
<div id="toast-message" class="toast toast-top toast-end opacity-0 transition-opacity duration-300">
  <div role="alert" class="alert alert-success">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <span>智能体已启动</span>
  </div>
</div>
</body>
</html> 