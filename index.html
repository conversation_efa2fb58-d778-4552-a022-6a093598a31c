<!DOCTYPE html>
<html lang="zh-CN" data-theme="lofi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>工作台 - 起号助手</title>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    .menu-item {
      font-size: 0.95rem;
      padding: 0.75rem 1rem;
    }
    .menu-item i {
      font-size: 1rem;
      margin-right: 0.75rem;
      width: 1.5rem;
      text-align: center;
    }
    .menu-active {
      background-color: #e5e7eb !important;
      color: #000000 !important;
      font-weight: 500;
      box-shadow: none !important;
    }
    .user-profile {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    .user-profile:hover {
      background-color: #f3f4f6;
    }
    
    /* 骨架屏样式 */
    .hidden-content {
      display: none;
    }
    
    /* 闪烁效果 */
    @keyframes pulse {
      0% {
        opacity: 0.6;
      }
      50% {
        opacity: 1;
      }
      100% {
        opacity: 0.6;
      }
    }
    
    .skeleton {
      animation: pulse 1.5s infinite;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 0.375rem;
    }
    
    /* 自定义状态指示器样式 */
    .status {
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 50%;
    }
    
    .status-success {
      background-color: hsl(var(--su));
    }
    
    .status-warning {
      background-color: hsl(var(--wa));
    }
    
    .status-error {
      background-color: hsl(var(--er));
    }
    
    /* 移动设备优化 */
    @media (max-width: 640px) {
      .mobile-grid {
        grid-template-columns: 1fr !important;
      }
    }
    
    /* Toast样式 */
    .toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      z-index: 9999;
      pointer-events: none;
    }
    
    .toast .alert {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .toast-top {
      top: 2rem;
    }
    
    .toast-end {
      right: 2rem;
    }
    
    /* 侧边栏徽章样式 */
    .menu-item .badge {
      font-size: 0.7rem;
      padding: 0.35rem 0.5rem;
      border-radius: 0.75rem;
      min-width: 1.6rem;
      text-align: center;
    }
    
    .menu-active .badge {
      background-color: #333333 !important;
      color: white !important;
    }
  </style>
</head>
<body class="bg-base-200">
  <div class="drawer lg:drawer-open">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col">
      <!-- 顶部栏，移动端可见 -->
      <div class="w-full navbar bg-base-100 shadow-sm lg:hidden">
        <div class="flex-none">
          <label for="drawer-toggle" class="btn btn-square btn-ghost">
            <i class="fas fa-bars"></i>
          </label>
        </div>
        <div class="flex-1">
          <span class="text-xl font-bold">工作台</span>
        </div>
      </div>
      
      <!-- 主内容区 -->
      <div class="p-4 lg:p-6">
        <!-- 头部问候 -->
        <header class="mb-6">
          <h1 class="text-2xl font-bold">欢迎回来，崔正 Chon</h1>
          <p class="text-base-content opacity-60">每一个爆款背后，都是无数次坚持和日拱一卒</p>
        </header>

        <!-- 添加素材按钮 -->
        <div class="flex flex-col md:flex-row items-start md:items-center gap-2 mb-6">
          <button class="btn bg-black text-white hover:bg-gray-800" onclick="add_material_modal.showModal()">
            <i class="fas fa-upload mr-2"></i>手动添加素材
          </button>
          <p class="text-sm opacity-70">手动添加素材后，将自动生成 5 个爆款选题。</p>
        </div>

        <!-- 添加素材模态框 -->
        <dialog id="add_material_modal" class="modal modal-bottom sm:modal-middle">
          <div class="modal-box">
            <h3 class="text-lg font-bold mb-4">手动添加素材</h3>
            <div class="mb-4">
              <div class="text-base font-medium mb-2">抖音链接</div>
              <textarea class="textarea textarea-bordered w-full h-24" placeholder="抖音视频的分享链接"></textarea>
            </div>
            <div class="modal-action">
              <form method="dialog" class="flex gap-2">
                <button class="btn">取消</button>
                <button class="btn bg-black text-white hover:bg-gray-800">确定</button>
              </form>
            </div>
          </div>
          <form method="dialog" class="modal-backdrop">
            <button>关闭</button>
          </form>
        </dialog>
        
        <!-- 基础配置骨架屏 -->
        <div id="skeleton-config">
          <h2 class="text-xl font-bold mb-4">基础配置</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div class="skeleton h-48 w-full"></div>
            <div class="skeleton h-48 w-full"></div>
            <div class="skeleton h-48 w-full"></div>
            <div class="skeleton h-48 w-full"></div>
          </div>
        </div>
        
        <!-- 基础配置实际内容 -->
        <div id="config-content" class="hidden-content">
          <!-- 设置卡片 -->
          <h2 class="text-xl font-bold mb-4">基础配置</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <!-- 商业定位 -->
            <div class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow">
              <div class="card-body">
                <h3 class="card-title text-lg">
                  <i class="fas fa-bullseye text-gray-600 mr-2"></i>商业定位
                </h3>
                <p class="text-sm opacity-60">设置您的商业目标、内容定位和人设</p>
                <div class="mt-4">
                  <div class="badge">未设置</div>
                </div>
                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-outline btn-sm">
                    去设置
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 抖音号 -->
            <div class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow">
              <div class="card-body">
                <h3 class="card-title text-lg">
                  <i class="fas fa-music text-gray-600 mr-2"></i>抖音号
                </h3>
                <p class="text-sm opacity-60">关联您的抖音账号</p>
                <div class="mt-4">
                  <div class="badge">已设置</div>
                </div>
                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-outline btn-sm">
                    修改
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 抖音Cookie -->
            <div class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow">
              <div class="card-body">
                <h3 class="card-title text-lg">
                  <i class="fas fa-cookie text-gray-600 mr-2"></i>抖音Cookie
                </h3>
                <p class="text-sm opacity-60">配置后可自动化抓取你的抖音收藏夹</p>
                <div class="mt-4">
                  <div class="badge">未设置</div>
                </div>
                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-outline btn-sm">
                    去设置
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 身份设置 -->
            <div class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow">
              <div class="card-body">
                <h3 class="card-title text-lg">
                  <i class="fas fa-user-gear text-gray-600 mr-2"></i>身份设置
                </h3>
                <p class="text-sm opacity-60">设置你的身份</p>
                <div class="mt-4">
                  <div class="badge">已设置</div>
                </div>
                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-outline btn-sm">
                    修改
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 数据概览骨架屏 -->
        <div id="skeleton-overview">
          <h2 class="text-xl font-bold mb-4">数据概览</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="skeleton h-24 w-full"></div>
            <div class="skeleton h-24 w-full"></div>
            <div class="skeleton h-24 w-full"></div>
            <div class="skeleton h-24 w-full"></div>
          </div>
        </div>
        
        <!-- 数据概览实际内容 -->
        <div id="overview-content" class="hidden-content">
          <h2 class="text-xl font-bold mb-4">数据概览</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="stat bg-base-100 shadow rounded-box">
              <div class="stat-figure text-base-content opacity-60">
                <i class="fas fa-photo-film text-3xl"></i>
              </div>
              <div class="stat-title opacity-60">素材总数</div>
              <div class="stat-value">80</div>
            </div>
            
            <div class="stat bg-base-100 shadow rounded-box">
              <div class="stat-figure text-base-content opacity-60">
                <i class="fas fa-lightbulb text-3xl"></i>
              </div>
              <div class="stat-title opacity-60">选题总数</div>
              <div class="stat-value">500</div>
            </div>
            
            <div class="stat bg-base-100 shadow rounded-box">
              <div class="stat-figure text-base-content opacity-60">
                <i class="fas fa-wrench text-3xl"></i>
              </div>
              <div class="stat-title opacity-60">公共工具数</div>
              <div class="stat-value">30</div>
            </div>
            
            <div class="stat bg-base-100 shadow rounded-box">
              <div class="stat-figure text-base-content opacity-60">
                <i class="fas fa-user-lock text-3xl"></i>
              </div>
              <div class="stat-title opacity-60">私有工具数（即将上线）</div>
              <div class="stat-value">0</div>
            </div>
          </div>
        </div>
        
        <!-- 版本更新骨架屏 -->
        <div id="skeleton-version">
          <h2 class="text-xl font-bold mb-4">版本更新</h2>
          <div class="grid grid-cols-1 gap-4">
            <div class="skeleton h-24 w-full"></div>
          </div>
        </div>
        
        <!-- 版本更新实际内容 -->
        <div id="version-content" class="hidden-content">
          <h2 class="text-xl font-bold mb-4">版本更新</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="card bg-base-100 shadow-md hover:shadow-lg transition-shadow">
              <div class="card-body">
                <div class="flex justify-between items-center">
                  <div>
                    <h3 class="card-title text-lg">
                      <i class="fas fa-code-branch text-gray-600 mr-2"></i>当前版本
                    </h3>
                    <p class="text-sm opacity-60">20250424-main-b425c10f</p>
                  </div>
                  <div class="text-lg font-bold">1.1.0</div>
                </div>
                <div class="card-actions justify-end mt-4">
                  <a href="https://tech-done.feishu.cn/docx/Nn0TdAfB0o81azx2bVBctP5An1c" target="_blank" class="btn btn-outline btn-sm">
                    更新说明
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="drawer-side">
      <label for="drawer-toggle" class="drawer-overlay"></label>
      <div class="bg-base-100 w-64 min-h-full p-4 flex flex-col">
        
        <ul class="menu menu-lg gap-2 flex-grow w-full">
          <li class="w-full">
            <a href="index.html" class="menu-item menu-active w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-home"></i>工作台
              </span>
            </a>
          </li>
          <li class="w-full">
            <a href="inbox.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-inbox"></i>灵感收件箱
              </span>
              <div class="badge badge-sm badge-ghost">28</div>
            </a>
            <ul class="space-y-2 mt-2 ml-6">
              <li><a href="douyin_collection.html" class="text-sm"><i class="fas fa-star mr-2"></i>同步抖音收藏夹</a></li>
              <li><a href="account_monitoring.html" class="text-sm"><i class="fas fa-eye mr-2"></i>对标账号监控</a></li>
              <li><a href="keyword_monitoring.html" class="text-sm"><i class="fas fa-search mr-2"></i>行业关键词监控</a></li>
            </ul>
          </li>
          <li class="w-full">
            <a href="assets.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-photo-film"></i>素材库
              </span>
              <div class="badge badge-sm badge-ghost">8</div>
            </a>
          </li>
          <li class="w-full">
            <a href="topics.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-lightbulb"></i>选题库
              </span>
              <div class="badge badge-sm badge-ghost">500</div>
            </a>
          </li>
          <li class="w-full">
            <a href="tools.html" class="menu-item w-full flex justify-between items-center">
              <span class="flex items-center">
                <i class="fas fa-robot"></i>智能体
              </span>
            </a>
          </li>
        </ul>
        
        <!-- 升级按钮 -->
        <div class="my-2">
          <a href="price.html" class="btn btn-outline btn-block">
            <i class="fas fa-crown mr-2"></i>升级为付费版
          </a>
        </div>
        
        <div class="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
          <div class="flex items-center">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img src="./assets/avatar_chon.jpg" alt="用户头像">
              </div>
            </div>
            <div class="ml-3">
              <p class="font-medium">崔正 Chon</p>
              <p class="text-xs opacity-60">创作者</p>
            </div>
          </div>
          <button class="btn btn-sm btn-ghost btn-circle" onclick="logout_confirm_modal.showModal()">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 退出登录确认模态框 -->
  <dialog id="logout_confirm_modal" class="modal modal-bottom sm:modal-middle">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">确认退出</h3>
      <p>您确定要退出登录吗？</p>
      <div class="modal-action">
        <form method="dialog" class="flex gap-2">
          <button class="btn">取消</button>
          <button class="btn btn-error text-white">确认退出</button>
        </form>
      </div>
    </div>
    <form method="dialog" class="modal-backdrop">
      <button>关闭</button>
    </form>
  </dialog>

  <!-- 骨架屏加载脚本 -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 模拟加载延迟
      setTimeout(function() {
        // 显示基础配置内容
        const skeletonConfig = document.getElementById('skeleton-config');
        const configContent = document.getElementById('config-content');
        
        if (skeletonConfig && configContent) {
          // 隐藏骨架屏
          skeletonConfig.style.display = 'none';
          
          // 显示基础配置内容
          configContent.classList.remove('hidden-content');
        }
        
        // 显示数据概览内容
        const skeletonOverview = document.getElementById('skeleton-overview');
        const overviewContent = document.getElementById('overview-content');
        
        if (skeletonOverview && overviewContent) {
          // 隐藏骨架屏
          skeletonOverview.style.display = 'none';
          
          // 显示数据概览内容
          overviewContent.classList.remove('hidden-content');
        }
        
        // 显示版本更新内容
        const skeletonVersion = document.getElementById('skeleton-version');
        const versionContent = document.getElementById('version-content');
        
        if (skeletonVersion && versionContent) {
          // 隐藏骨架屏
          skeletonVersion.style.display = 'none';
          
          // 显示版本更新内容
          versionContent.classList.remove('hidden-content');
        }
      }, 300); // 300毫秒后显示内容
    });
  </script>

  <!-- 成功提示Toast，一开始隐藏 -->
  <div id="toast-message" class="toast toast-top toast-end opacity-0 transition-opacity duration-300">
    <div role="alert" class="alert alert-success">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>设置已保存</span>
    </div>
  </div>

  <script>
    // 显示Toast提示
    function showToast(message, type = 'success') {
      // 创建toast元素
      let toast = document.getElementById('toast-message');
      if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast-message';
        toast.className = 'toast toast-top toast-end opacity-0 transition-opacity duration-300';
        
        let alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-error';
        else if (type === 'warning') alertClass = 'alert-warning';
        else if (type === 'info') alertClass = 'alert-info';
        
        toast.innerHTML = `
          <div role="alert" class="alert ${alertClass}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>${message}</span>
          </div>
        `;
        document.body.appendChild(toast);
      } else {
        toast.querySelector('span').textContent = message;
        
        // 更新alert类型
        let alertDiv = toast.querySelector('.alert');
        alertDiv.classList.remove('alert-success', 'alert-error', 'alert-warning', 'alert-info');
        
        let alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-error';
        else if (type === 'warning') alertClass = 'alert-warning';
        else if (type === 'info') alertClass = 'alert-info';
        
        alertDiv.classList.add(alertClass);
      }
      
      // 显示Toast
      toast.classList.remove('opacity-0');
      toast.classList.add('opacity-100');
      
      // 2秒后自动隐藏
      setTimeout(() => {
        toast.classList.remove('opacity-100');
        toast.classList.add('opacity-0');
      }, 2000);
    }
    
    // 保存设置函数
    function saveSettings() {
      showToast('设置已保存');
    }
    
    // 更新侧边栏数量
    function updateSidebarCounts() {
      // 计算素材总数
      const materialCount = 8; // 示例数据
      
      // 计算已确认选题总数
      const topicCount = 5; // 示例数据
      
      // 更新侧边栏徽章
      const materialsBadge = document.querySelector('a[href="assets.html"] .badge');
      const topicsBadge = document.querySelector('a[href="topics.html"] .badge');
      
      if (materialsBadge) {
        materialsBadge.textContent = materialCount;
      }
      
      if (topicsBadge) {
        topicsBadge.textContent = topicCount;
      }
    }
    
    // 页面加载时更新侧边栏数量
    document.addEventListener('DOMContentLoaded', function() {
      updateSidebarCounts();
      
      // 为保存按钮添加事件
      const saveButtons = document.querySelectorAll('.btn-save');
      saveButtons.forEach(button => {
        button.addEventListener('click', function() {
          saveSettings();
        });
      });
    });
  </script>
</body>
</html> 