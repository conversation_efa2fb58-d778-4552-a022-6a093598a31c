name: tag prod deploy

env:
  SERVICE_NAME: "[设计] 起号助手 App Design"

on:
  push:
    tags:
      - 'v*'

jobs:

  vars:
    needs: []
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      
      - name: Get version
        id: get_version
        run: |
          SHORT_HASH=${GITHUB_SHA:0:7}
          echo "version=$SHORT_HASH" >> $GITHUB_OUTPUT
          LATEST_TAG=$(git describe --tags `git rev-list --tags --max-count=1`)
          echo "LATEST_TAG=$LATEST_TAG" >> $GITHUB_ENV

      - run: echo vars
    outputs:
      service-name: ${{ env.SERVICE_NAME }}
      version: ${{ steps.get_version.outputs.version }}
      latest-tag: ${{ env.LATEST_TAG }}


  build:
    needs: [vars]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: upload files to OSS
        uses: tvrcgo/oss-action@master
        with:
            key-id: ${{ secrets.ACCESS_KEY_ID }}
            key-secret: ${{ secrets.ACCESS_KEY_SECRET }}
            region: oss-cn-shanghai
            bucket: qihaozhushou-design
            assets: |
              ./**:/${{ needs.vars.outputs.latest-tag }}/


  feishu-success-notice:
    needs: [build, vars]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: "[设计] 起号助手 App Design"
      service-url: https://test.design.qihaozhushou.com
      environment-name: prod


  feishu-failure-notice:
    if: failure()
    needs: [build, vars]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: "[设计] 起号助手 App Design"
      version: ${{ needs.vars.outputs.version }}
      environment-name: prod