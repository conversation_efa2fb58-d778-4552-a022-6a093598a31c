name: prod deploy

env:
  SERVICE_NAME: "[设计] 起号助手 App Design"

on:
  push:
    branches:
      - main

jobs:
  vars:
    needs: []
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      
      - name: Get version
        id: get_version
        run: |
          SHORT_HASH=${GITHUB_SHA:0:7}
          echo "version=$SHORT_HASH" >> $GITHUB_OUTPUT
      
      - run: echo vars
    outputs:
      service-name: ${{ env.SERVICE_NAME }}
      version: ${{ steps.get_version.outputs.version }}

  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: upload files to OSS
        uses: fangbinwei/aliyun-oss-website-action@v1
        with:
            accessKeyId: ${{ secrets.ACCESS_KEY_ID }}
            accessKeySecret: ${{ secrets.ACCESS_KEY_SECRET }}
            bucket: qihaozhushou-design
            endpoint: oss-cn-shanghai.aliyuncs.com
            folder: ./


  feishu-success-notice:
    needs: [build, vars]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: "[设计] 起号助手 App Design"
      service-url: https://test.design.qihaozhushou.com
      environment-name: prod


  feishu-failure-notice:
    if: failure()
    needs: [build, vars]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: "[设计] 起号助手 App Design"
      version: ${{ needs.vars.outputs.version }}
      environment-name: prod